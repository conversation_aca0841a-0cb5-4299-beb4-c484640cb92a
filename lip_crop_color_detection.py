#!/usr/bin/env python3
"""
Lip detection using color-based detection for pink/red lips.
Much more accurate than face detection for finding actual lips.
"""

import cv2
import numpy as np
import os
import sys

def detect_lips_by_color(frame, debug=False):
    """
    Detect lips using HSV color space to find pink/red lip colors.
    Returns the center position of the detected lip region.
    """
    # Convert to HSV for better color detection
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    
    # Define multiple ranges for lip colors (pink, red, light red)
    # HSV ranges for different lip tones
    lip_ranges = [
        # Pink lips
        ([0, 50, 50], [10, 255, 255]),      # Light red/pink
        ([160, 50, 50], [180, 255, 255]),   # Red/pink (wrapping around)
        
        # Darker red lips
        ([0, 70, 70], [15, 255, 200]),      # Red
        ([165, 70, 70], [180, 255, 200]),   # Dark red
        
        # Light pink lips
        ([0, 30, 100], [20, 150, 255]),     # Very light pink
        ([160, 30, 100], [180, 150, 255]),  # Light pink (wrap)
    ]
    
    # Create combined mask for all lip color ranges
    combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
    
    for lower, upper in lip_ranges:
        lower = np.array(lower)
        upper = np.array(upper)
        mask = cv2.inRange(hsv, lower, upper)
        combined_mask = cv2.bitwise_or(combined_mask, mask)
    
    # Clean up the mask
    kernel = np.ones((3,3), np.uint8)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
    
    # Find contours
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if debug:
        print(f"DEBUG: Found {len(contours)} lip color regions")
    
    if len(contours) == 0:
        return None, None, combined_mask if debug else None
    
    # Filter contours by size and aspect ratio (lips are typically wider than tall)
    valid_contours = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 20:  # Too small
            continue
            
        # Get bounding rectangle
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        # Lips are typically wider than tall (aspect ratio > 1)
        # But not too wide (aspect ratio < 4)
        if 0.8 < aspect_ratio < 4 and area > 50:
            valid_contours.append((contour, area, x, y, w, h))
            if debug:
                print(f"DEBUG: Valid lip region: area={area}, aspect_ratio={aspect_ratio:.2f}, pos=({x},{y}), size={w}x{h}")
    
    if len(valid_contours) == 0:
        if debug:
            print("DEBUG: No valid lip regions found")
        return None, None, combined_mask if debug else None
    
    # Choose the largest valid contour (most likely to be lips)
    best_contour = max(valid_contours, key=lambda x: x[1])  # Sort by area
    contour, area, x, y, w, h = best_contour
    
    # Calculate center of the lip region
    lip_center_x = x + w // 2
    lip_center_y = y + h // 2
    
    if debug:
        print(f"DEBUG: Selected lip region: center=({lip_center_x}, {lip_center_y}), area={area}")
    
    return lip_center_x, lip_center_y, combined_mask if debug else None

def find_optimal_lip_position_color(video_path):
    """
    Find lip position using color-based detection across multiple frames.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Video has {total_frames} frames")
    
    # Sample frames throughout the video
    if total_frames > 50:
        sample_frames = list(range(10, min(total_frames-10, 70), 3))  # Every 3rd frame
    else:
        sample_frames = list(range(5, total_frames-5, 2))  # Every 2nd frame
    
    detected_positions = []
    
    print("Finding lips using color detection...")
    print(f"Checking frames: {sample_frames}")
    
    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            lip_x, lip_y, _ = detect_lips_by_color(frame, debug=(frame_num == sample_frames[0]))
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Detected lips at ({lip_x}, {lip_y})")
            else:
                print(f"Frame {frame_num}: No lips detected")
    
    cap.release()
    
    if not detected_positions:
        print("Could not detect lips in any frames!")
        return None, None
    
    print(f"Successfully detected lips in {len(detected_positions)} frames")
    
    # Use median position for stability
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]
    
    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))
    
    print(f"Final lip center: ({median_x}, {median_y})")
    return median_x, median_y

def crop_with_guaranteed_center_color(frame, lip_x, lip_y, output_width=96, output_height=64, zoom_factor=0.66, show_debug=False):
    """
    Crop with lips guaranteed at center using color detection results.
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Target center in output
    target_center_x = output_width // 2   # 48
    target_center_y = output_height // 2  # 32
    
    # Calculate crop dimensions
    crop_width = int(output_width * zoom_factor)
    crop_height = int(output_height * zoom_factor)
    
    if show_debug:
        print(f"DEBUG: Target center in output: ({target_center_x}, {target_center_y})")
        print(f"DEBUG: Crop size: {crop_width}x{crop_height}")
        print(f"DEBUG: Lip position in source: ({lip_x}, {lip_y})")
    
    # Calculate ideal crop boundaries (centered on lips)
    ideal_left = lip_x - crop_width // 2
    ideal_right = lip_x + crop_width // 2
    ideal_top = lip_y - crop_height // 2
    ideal_bottom = lip_y + crop_height // 2
    
    # Calculate actual crop boundaries (within frame limits)
    actual_left = max(0, ideal_left)
    actual_right = min(frame_w, ideal_right)
    actual_top = max(0, ideal_top)
    actual_bottom = min(frame_h, ideal_bottom)
    
    # Crop the available region
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_crop_h, actual_crop_w = cropped.shape[:2]
    
    if actual_crop_w == crop_width and actual_crop_h == crop_height:
        # Perfect crop - just resize and lips will be centered
        if show_debug:
            print("DEBUG: Perfect crop - resizing directly")
        resized = cv2.resize(cropped, (output_width, output_height))
        return resized
    else:
        # Imperfect crop due to boundaries - need to maintain lip centering
        if show_debug:
            print("DEBUG: Imperfect crop - maintaining lip centering")
        
        # Create output canvas
        output_canvas = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        
        # Calculate where the lip appears in the cropped region
        lip_in_crop_x = lip_x - actual_left
        lip_in_crop_y = lip_y - actual_top
        
        # Scale the cropped region
        scale_x = output_width / actual_crop_w
        scale_y = output_height / actual_crop_h
        scale = min(scale_x, scale_y)
        
        scaled_width = int(actual_crop_w * scale)
        scaled_height = int(actual_crop_h * scale)
        scaled_cropped = cv2.resize(cropped, (scaled_width, scaled_height))
        
        # Calculate where lip will be in scaled image
        lip_in_scaled_x = int(lip_in_crop_x * scale)
        lip_in_scaled_y = int(lip_in_crop_y * scale)
        
        # Calculate paste position to center the lips
        paste_x = target_center_x - lip_in_scaled_x
        paste_y = target_center_y - lip_in_scaled_y
        
        # Ensure paste position is valid
        paste_x = max(0, min(paste_x, output_width - scaled_width))
        paste_y = max(0, min(paste_y, output_height - scaled_height))
        
        if show_debug:
            print(f"DEBUG: Final lip position will be: ({lip_in_scaled_x + paste_x}, {lip_in_scaled_y + paste_y})")
        
        # Paste the scaled image
        output_canvas[paste_y:paste_y + scaled_height, paste_x:paste_x + scaled_width] = scaled_cropped
        
        return output_canvas

def process_video_color_detection(input_path, output_path, zoom_factor=0.66):
    """
    Process video using color-based lip detection.
    """
    # Find lip position using color detection
    lip_x, lip_y = find_optimal_lip_position_color(input_path)
    
    if lip_x is None:
        print("Could not detect lips using color detection!")
        return False
    
    print(f"Using lip position: ({lip_x}, {lip_y})")
    print(f"Zoom factor: {zoom_factor}")
    print("GUARANTEED: Lips will be at exact center (48, 32) in output")
    
    # Process video
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    print(f"\nProcessing {total_frames} frames...")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Show debug info only for first frame
        if frame_count == 1:
            print(f"\nDEBUG INFO FOR FIRST FRAME:")
        
        # Process frame
        cropped = crop_with_guaranteed_center_color(frame, lip_x, lip_y, 96, 64, zoom_factor, frame_count == 1)
        out.write(cropped)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Complete! Saved: {output_path}")
    return True

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    output_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor_lips_color_detection.mp4"
    
    print("Creating lip video using COLOR-BASED detection...")
    print("This will detect actual pink/red lip colors instead of faces")
    print("Much more accurate for finding real lips!")
    
    success = process_video_color_detection(input_video, output_video, zoom_factor=0.66)
    
    if success:
        print(f"\n✅ Success! Created: doctor_lips_color_detection.mp4")
        print("Lips detected by COLOR and centered at (48, 32)!")
    else:
        print(f"\n❌ Failed to create video")

if __name__ == "__main__":
    main()
