#!/usr/bin/env python3
"""
Final lip cropping script with manual position adjustment capability.
"""

import cv2
import numpy as np
import os
import sys

def detect_lips_improved(frame):
    """
    Improved lip detection focusing on the actual lip center.
    """
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    
    # More precise lip color ranges
    # Red lips
    lower_red1 = np.array([0, 70, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([170, 70, 50])
    upper_red2 = np.array([180, 255, 255])
    
    # Pink lips
    lower_pink = np.array([140, 40, 50])
    upper_pink = np.array([170, 255, 255])
    
    # Create masks
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask3 = cv2.inRange(hsv, lower_pink, upper_pink)
    
    # Combine all masks
    combined_mask = cv2.bitwise_or(mask1, mask2)
    combined_mask = cv2.bitwise_or(combined_mask, mask3)
    
    # Clean up the mask
    kernel = np.ones((3,3), np.uint8)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
    
    # Find contours
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None, None
    
    # Filter contours by position (lower 2/3 of frame) and size
    frame_height = frame.shape[0]
    valid_contours = []
    
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        area = cv2.contourArea(contour)
        
        # Must be in lower 2/3 of frame and reasonable size
        if y > frame_height // 3 and 50 < area < 3000 and w > 8 and h > 4:
            valid_contours.append(contour)
    
    if not valid_contours:
        return None, None
    
    # Use the largest valid contour
    best_contour = max(valid_contours, key=cv2.contourArea)
    
    # Get the center of mass of the contour
    M = cv2.moments(best_contour)
    if M["m00"] != 0:
        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])
        return center_x, center_y
    
    return None, None

def find_optimal_lip_position(video_path, manual_adjust_y=0):
    """
    Find the best lip position with optional manual adjustment.
    manual_adjust_y: positive values move lips up, negative values move lips down
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Sample more frames for better accuracy
    sample_frames = np.linspace(5, total_frames-5, 15, dtype=int)
    
    detected_positions = []
    
    print("Analyzing frames for optimal lip position...")
    
    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            lip_x, lip_y = detect_lips_improved(frame)
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Lips at ({lip_x}, {lip_y})")
    
    cap.release()
    
    if not detected_positions:
        print("Could not detect lips in any frames!")
        return None, None
    
    # Use median position for stability
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]
    
    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))
    
    # Apply manual adjustment
    adjusted_y = median_y + manual_adjust_y
    
    print(f"\nDetected lip center: ({median_x}, {median_y})")
    if manual_adjust_y != 0:
        print(f"Manual Y adjustment: {manual_adjust_y}")
        print(f"Final lip position: ({median_x}, {adjusted_y})")
    
    return median_x, adjusted_y

def crop_lips_perfectly_centered(frame, lip_x, lip_y, width=96, height=64):
    """
    Crop frame with lips at exact center.
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Target center coordinates
    center_x = width // 2   # 48
    center_y = height // 2  # 32
    
    # Calculate crop boundaries
    left = lip_x - center_x
    right = lip_x + center_x  
    top = lip_y - center_y
    bottom = lip_y + center_y
    
    # Handle boundaries
    actual_left = max(0, left)
    actual_right = min(frame_w, right)
    actual_top = max(0, top)
    actual_bottom = min(frame_h, bottom)
    
    # Crop
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    
    # If perfect size, return as-is
    if cropped.shape[1] == width and cropped.shape[0] == height:
        return cropped
    
    # Otherwise, pad with black to exact size
    output = np.zeros((height, width, 3), dtype=np.uint8)
    
    # Calculate paste position to maintain centering
    paste_x = max(0, actual_left - left)
    paste_y = max(0, actual_top - top)
    
    # Ensure we don't exceed boundaries
    crop_h, crop_w = cropped.shape[:2]
    paste_x = min(paste_x, width - crop_w)
    paste_y = min(paste_y, height - crop_h)
    
    # Paste the cropped region
    output[paste_y:paste_y + crop_h, paste_x:paste_x + crop_w] = cropped
    
    return output

def process_video_final(input_path, output_path, manual_y_adjust=0):
    """
    Process video with perfect lip centering.
    manual_y_adjust: positive moves lips up in detection, negative moves down
    """
    # Find optimal lip position
    lip_x, lip_y = find_optimal_lip_position(input_path, manual_y_adjust)
    
    if lip_x is None:
        print("Could not detect lips!")
        return False
    
    # Process video
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    print(f"\nProcessing {total_frames} frames...")
    print(f"Lip position: ({lip_x}, {lip_y})")
    print("Lips will be at exact center (48, 32) of 96x64 output")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Crop with lips perfectly centered
        cropped = crop_lips_perfectly_centered(frame, lip_x, lip_y)
        out.write(cropped)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Complete! Saved: {output_path}")
    return True

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    
    # Try with different Y adjustments if needed
    adjustments_to_try = [0, -10, -20, -30, 10, 20]  # Negative moves lips up in final output
    
    for i, y_adjust in enumerate(adjustments_to_try):
        output_name = f"bbaf2n_final_centered_adj{y_adjust}.mp4" if y_adjust != 0 else "bbaf2n_final_centered.mp4"
        output_path = f"/Users/<USER>/Desktop/preprocessing_pipeline/{output_name}"
        
        print(f"\n{'='*50}")
        print(f"Attempt {i+1}: Y adjustment = {y_adjust}")
        print(f"Output: {output_name}")
        
        success = process_video_final(input_video, output_path, y_adjust)
        
        if success:
            print(f"✅ Created: {output_name}")
            if i == 0:  # Only create the first one for now
                break
        else:
            print(f"❌ Failed: {output_name}")

if __name__ == "__main__":
    main()
