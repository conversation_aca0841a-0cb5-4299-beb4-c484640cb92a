#!/usr/bin/env python3
"""
Detect speaking lips by analyzing movement and color changes across frames.
Speaking lips will show more variation than static features.
"""

import cv2
import numpy as np

def detect_speaking_lips(video_path):
    """
    Detect lips by finding areas that change the most during speech.
    Speaking lips will have more variation than static features.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"Analyzing {total_frames} frames for speaking motion...")
    
    # Sample frames throughout the video
    sample_frames = list(range(10, min(total_frames-10, 70), 2))  # Every 2nd frame
    frames = []
    
    # Read sample frames
    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        if ret:
            # Convert to grayscale for motion analysis
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            frames.append(gray)
    
    cap.release()
    
    if len(frames) < 5:
        print("Not enough frames for analysis")
        return None, None
    
    print(f"Analyzing motion in {len(frames)} frames...")
    
    # Calculate frame differences to find areas of movement
    motion_map = np.zeros_like(frames[0], dtype=np.float32)
    
    for i in range(1, len(frames)):
        diff = cv2.absdiff(frames[i-1], frames[i])
        motion_map += diff.astype(np.float32)
    
    # Normalize motion map
    motion_map = motion_map / len(frames)
    
    # Apply Gaussian blur to smooth the motion map
    motion_map = cv2.GaussianBlur(motion_map, (15, 15), 0)
    
    # Save motion map for debugging
    motion_map_normalized = (motion_map / motion_map.max() * 255).astype(np.uint8)
    cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/motion_map.jpg", motion_map_normalized)
    
    # Find regions with high motion (likely lips during speech)
    threshold = np.percentile(motion_map, 85)  # Top 15% of motion
    motion_mask = (motion_map > threshold).astype(np.uint8) * 255
    
    # Clean up the mask
    kernel = np.ones((5,5), np.uint8)
    motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_OPEN, kernel)
    motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)
    
    # Save motion mask for debugging
    cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/motion_mask.jpg", motion_mask)
    
    # Find contours in motion mask
    contours, _ = cv2.findContours(motion_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if len(contours) == 0:
        print("No motion regions found")
        return None, None
    
    # Filter contours by size and position
    valid_regions = []
    frame_h, frame_w = frames[0].shape
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 100:  # Too small
            continue
            
        x, y, w, h = cv2.boundingRect(contour)
        
        # Lips are typically in the lower half of the frame for a seated person
        if y < frame_h * 0.3:  # Skip upper regions (likely eyes/forehead)
            continue
            
        # Lips have a reasonable aspect ratio
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio < 0.5 or aspect_ratio > 3:  # Too thin or too wide
            continue
            
        center_x = x + w // 2
        center_y = y + h // 2
        
        valid_regions.append((area, center_x, center_y, x, y, w, h))
        print(f"Motion region: center=({center_x}, {center_y}), area={area}, aspect_ratio={aspect_ratio:.2f}")
    
    if len(valid_regions) == 0:
        print("No valid motion regions found")
        return None, None
    
    # Choose the region with most motion in the lower part of frame
    # Weight by area and position (prefer lower regions)
    best_region = None
    best_score = 0
    
    for area, center_x, center_y, x, y, w, h in valid_regions:
        # Score based on area and vertical position (lower is better for lips)
        position_weight = center_y / frame_h  # Higher for lower positions
        score = area * position_weight
        
        if score > best_score:
            best_score = score
            best_region = (center_x, center_y)
    
    if best_region:
        print(f"Best motion region (likely lips): {best_region}")
        
        # Create visualization
        vis_frame = cv2.cvtColor(frames[len(frames)//2], cv2.COLOR_GRAY2BGR)
        cv2.circle(vis_frame, best_region, 10, (0, 255, 0), 2)
        cv2.putText(vis_frame, f"Lips: {best_region}", (best_region[0]-30, best_region[1]-15), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/detected_lips.jpg", vis_frame)
        
        return best_region[0], best_region[1]
    
    return None, None

def create_speaking_lips_video(input_path, output_path, lip_x, lip_y, zoom_factor=0.7):
    """
    Create video with lips centered, using slightly more zoom out for speaking motion.
    """
    print(f"Creating video with speaking lips at ({lip_x}, {lip_y})")
    print(f"Using zoom factor {zoom_factor} (more room for speech movement)")
    
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    # Calculate crop size with extra room for speech movement
    crop_width = int(96 * zoom_factor)
    crop_height = int(64 * zoom_factor)
    
    print(f"Crop size: {crop_width}x{crop_height} (gives room for lip movement)")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Calculate crop boundaries centered on lips
        left = max(0, lip_x - crop_width // 2)
        right = min(frame.shape[1], left + crop_width)
        top = max(0, lip_y - crop_height // 2)
        bottom = min(frame.shape[0], top + crop_height)
        
        # Ensure we have the full crop size
        if right - left < crop_width:
            if left == 0:
                right = min(frame.shape[1], crop_width)
            else:
                left = max(0, frame.shape[1] - crop_width)
                right = frame.shape[1]
        
        if bottom - top < crop_height:
            if top == 0:
                bottom = min(frame.shape[0], crop_height)
            else:
                top = max(0, frame.shape[0] - crop_height)
                bottom = frame.shape[0]
        
        # Crop and resize
        cropped = frame[top:bottom, left:right]
        if cropped.size > 0:
            resized = cv2.resize(cropped, (96, 64))
            out.write(resized)
        else:
            # Black frame if crop failed
            black_frame = np.zeros((64, 96, 3), dtype=np.uint8)
            out.write(black_frame)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Created: {output_path}")

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    
    print("SPEAKING LIPS DETECTOR")
    print("======================")
    print("Analyzing video for areas with most movement (speaking lips)...")
    
    # Detect lips by motion analysis
    lip_x, lip_y = detect_speaking_lips(input_video)
    
    if lip_x is None:
        print("❌ Could not detect speaking lips automatically")
        print("Please check the test_crop_*.jpg files from the previous script")
        print("and tell me which position shows the lips best")
        return
    
    print(f"✅ Detected speaking lips at: ({lip_x}, {lip_y})")
    
    # Create video with detected position
    output_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor_speaking_lips_detected.mp4"
    create_speaking_lips_video(input_video, output_video, lip_x, lip_y, zoom_factor=0.7)
    
    print(f"\n🎯 Created: doctor_speaking_lips_detected.mp4")
    print("This video should have the speaking lips centered with room for movement!")
    
    # Also create versions with slightly different positions for fine-tuning
    adjustments = [(-10, 0, "left10"), (10, 0, "right10"), (0, -10, "up10"), (0, 10, "down10")]
    
    print("\nCreating fine-tuned versions...")
    for dx, dy, suffix in adjustments:
        adj_x, adj_y = lip_x + dx, lip_y + dy
        output_adj = f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_speaking_lips_{suffix}.mp4"
        create_speaking_lips_video(input_video, output_adj, adj_x, adj_y, zoom_factor=0.7)
        print(f"Created: doctor_speaking_lips_{suffix}.mp4")

if __name__ == "__main__":
    main()
