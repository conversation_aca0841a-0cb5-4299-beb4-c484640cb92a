#!/usr/bin/env python3
"""
Simple approach: Let's manually examine the video and try different positions
based on what we can see in the frames.
"""

import cv2
import numpy as np

def create_test_crops():
    """
    Create test crops from different positions to see what we get.
    """
    video_path = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("Could not open video")
        return
    
    # Get a frame from the middle of the video
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    middle_frame = total_frames // 2
    
    cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
    ret, frame = cap.read()
    
    if not ret:
        print("Could not read frame")
        return
    
    print(f"Frame size: {frame.shape[1]}x{frame.shape[0]}")
    
    # Save the full frame for reference
    cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/doctor_reference_frame.jpg", frame)
    print("Saved reference frame: doctor_reference_frame.jpg")
    
    # Try different positions across the frame
    # Since it's 400x200, let's try a grid of positions
    test_positions = []
    
    # Create a grid of test positions
    for x in range(50, 350, 50):  # x from 50 to 300 in steps of 50
        for y in range(30, 170, 30):  # y from 30 to 150 in steps of 30
            test_positions.append((x, y))
    
    print(f"Testing {len(test_positions)} positions...")
    
    # Create crops for each position
    crop_size = 63  # This is what we calculated for 0.66 zoom factor
    crop_height = 42
    
    for i, (x, y) in enumerate(test_positions):
        # Calculate crop boundaries
        left = max(0, x - crop_size // 2)
        right = min(frame.shape[1], left + crop_size)
        top = max(0, y - crop_height // 2)
        bottom = min(frame.shape[0], top + crop_height)
        
        # Crop and resize
        cropped = frame[top:bottom, left:right]
        if cropped.size > 0:
            resized = cv2.resize(cropped, (96, 64))
            
            # Add position text
            cv2.putText(resized, f"({x},{y})", (5, 15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # Save crop
            filename = f"/Users/<USER>/Desktop/preprocessing_pipeline/test_crop_{x:03d}_{y:03d}.jpg"
            cv2.imwrite(filename, resized)
    
    cap.release()
    print(f"Created {len(test_positions)} test crops")
    print("Look through the test_crop_*.jpg files to find which position shows the lips best")
    print("The filename format is test_crop_XXX_YYY.jpg where XXX,YYY is the center position")

def create_video_with_position(lip_x, lip_y):
    """
    Create a video with the specified lip position.
    """
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    output_video = f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_lips_position_{lip_x}_{lip_y}.mp4"
    
    cap = cv2.VideoCapture(input_video)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video, fourcc, fps, (96, 64))
    
    print(f"Creating video with lips at ({lip_x}, {lip_y})")
    
    crop_width = 63  # 96 * 0.66
    crop_height = 42  # 64 * 0.66
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Calculate crop boundaries centered on lip position
        left = max(0, lip_x - crop_width // 2)
        right = min(frame.shape[1], left + crop_width)
        top = max(0, lip_y - crop_height // 2)
        bottom = min(frame.shape[0], top + crop_height)
        
        # Crop and resize
        cropped = frame[top:bottom, left:right]
        if cropped.size > 0:
            resized = cv2.resize(cropped, (96, 64))
            out.write(resized)
        else:
            # Black frame if crop failed
            black_frame = np.zeros((64, 96, 3), dtype=np.uint8)
            out.write(black_frame)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Created: {output_video}")

if __name__ == "__main__":
    print("SIMPLE LIP FINDER")
    print("=================")
    print("Step 1: Creating test crops from different positions...")
    
    create_test_crops()
    
    print("\nStep 2: Now look at the test_crop_*.jpg files")
    print("Find the one that shows the lips best and note the position from the filename")
    print("\nStep 3: Once you find the best position, I can create the final video")
    print("For example, if test_crop_200_120.jpg looks best, the lips are at (200, 120)")
