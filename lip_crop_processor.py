#!/usr/bin/env python3
"""
Video processing script to crop around lips region.
Crops video frames to 96x64 pixels centered on the speaker's lips.
"""

import cv2
import numpy as np
import os
import sys
from pathlib import Path

def detect_face_and_lips(frame, face_cascade, mouth_cascade):
    """
    Detect face and mouth region in the frame.
    Returns the center coordinates of the mouth region.
    """
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    
    # Detect faces
    faces = face_cascade.detectMultiScale(gray, 1.3, 5)
    
    if len(faces) == 0:
        return None, None
    
    # Use the largest face (assuming it's the main speaker)
    face = max(faces, key=lambda x: x[2] * x[3])
    x, y, w, h = face
    
    # Define mouth region within the face (lower third of face)
    mouth_y_start = y + int(h * 0.6)
    mouth_y_end = y + h
    mouth_roi = gray[mouth_y_start:mouth_y_end, x:x+w]
    
    # Detect mouth within the face region
    mouths = mouth_cascade.detectMultiScale(mouth_roi, 1.3, 5)
    
    if len(mouths) > 0:
        # Use the first detected mouth
        mx, my, mw, mh = mouths[0]
        # Convert coordinates back to full frame
        mouth_center_x = x + mx + mw // 2
        mouth_center_y = mouth_y_start + my + mh // 2
        return mouth_center_x, mouth_center_y
    else:
        # Fallback: estimate mouth position as lower center of face
        mouth_center_x = x + w // 2
        mouth_center_y = y + int(h * 0.8)
        return mouth_center_x, mouth_center_y

def crop_around_lips(frame, center_x, center_y, width=96, height=64):
    """
    Crop a region around the lips center with specified dimensions.
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Calculate crop boundaries
    left = max(0, center_x - width // 2)
    right = min(frame_w, center_x + width // 2)
    top = max(0, center_y - height // 2)
    bottom = min(frame_h, center_y + height // 2)
    
    # Adjust if we're at frame boundaries
    actual_width = right - left
    actual_height = bottom - top
    
    cropped = frame[top:bottom, left:right]
    
    # Resize to exact dimensions if needed
    if actual_width != width or actual_height != height:
        cropped = cv2.resize(cropped, (width, height))
    
    return cropped

class PositionSmoother:
    """
    Smooths position coordinates over time to reduce jitter.
    """
    def __init__(self, smoothing_factor=0.7, max_jump=20):
        self.smoothing_factor = smoothing_factor
        self.max_jump = max_jump
        self.smoothed_x = None
        self.smoothed_y = None
        self.position_history = []
        self.history_size = 5

    def smooth_position(self, x, y):
        """Apply smoothing to reduce jitter."""
        if self.smoothed_x is None:
            self.smoothed_x = x
            self.smoothed_y = y
            return x, y

        # Calculate distance from last position
        distance = np.sqrt((x - self.smoothed_x)**2 + (y - self.smoothed_y)**2)

        # If jump is too large, use more conservative smoothing
        if distance > self.max_jump:
            factor = 0.3
        else:
            factor = self.smoothing_factor

        # Apply exponential smoothing
        self.smoothed_x = factor * x + (1 - factor) * self.smoothed_x
        self.smoothed_y = factor * y + (1 - factor) * self.smoothed_y

        # Keep position history for additional stability
        self.position_history.append((self.smoothed_x, self.smoothed_y))
        if len(self.position_history) > self.history_size:
            self.position_history.pop(0)

        # Use median of recent positions for extra stability
        if len(self.position_history) >= 3:
            x_vals = [pos[0] for pos in self.position_history]
            y_vals = [pos[1] for pos in self.position_history]
            median_x = np.median(x_vals)
            median_y = np.median(y_vals)

            # Blend smoothed and median positions
            final_x = 0.8 * self.smoothed_x + 0.2 * median_x
            final_y = 0.8 * self.smoothed_y + 0.2 * median_y
        else:
            final_x, final_y = self.smoothed_x, self.smoothed_y

        return int(final_x), int(final_y)

def process_video(input_path, output_path, target_width=96, target_height=64):
    """
    Process the video to crop around lips region with stabilization.
    """
    # Load face and mouth cascade classifiers
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')

    # Initialize position smoother
    smoother = PositionSmoother(smoothing_factor=0.8, max_jump=15)

    # Open input video
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        raise ValueError(f"Could not open video file: {input_path}")

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Processing video: {input_path}")
    print(f"FPS: {fps}, Total frames: {total_frames}")
    print(f"Target dimensions: {target_width}x{target_height}")
    print("Using stabilization to reduce shakiness...")

    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (target_width, target_height))

    frame_count = 0
    last_mouth_center = None
    detection_failures = 0

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1

        # Detect lips/mouth center
        mouth_x, mouth_y = detect_face_and_lips(frame, face_cascade, mouth_cascade)

        if mouth_x is not None and mouth_y is not None:
            # Apply smoothing to detected position
            smooth_x, smooth_y = smoother.smooth_position(mouth_x, mouth_y)
            last_mouth_center = (smooth_x, smooth_y)
            detection_failures = 0
        elif last_mouth_center is not None:
            # Use last known position if detection fails
            smooth_x, smooth_y = last_mouth_center
            detection_failures += 1
        else:
            # Fallback to center of frame
            smooth_x, smooth_y = frame.shape[1] // 2, frame.shape[0] // 2
            smooth_x, smooth_y = smoother.smooth_position(smooth_x, smooth_y)
            last_mouth_center = (smooth_x, smooth_y)

        # Crop around lips using smoothed position
        cropped_frame = crop_around_lips(frame, smooth_x, smooth_y, target_width, target_height)

        # Write frame
        out.write(cropped_frame)

        # Progress indicator
        if frame_count % 30 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames} frames)")

    # Cleanup
    cap.release()
    out.release()
    cv2.destroyAllWindows()

    print(f"Processing complete! Output saved to: {output_path}")
    if detection_failures > 0:
        print(f"Note: Had {detection_failures} detection failures, used position smoothing")

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    output_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n_lips_cropped_stabilized.mp4"

    # Check if input file exists
    if not os.path.exists(input_video):
        print(f"Error: Input video file not found: {input_video}")
        sys.exit(1)

    try:
        process_video(input_video, output_video, target_width=96, target_height=64)
        print(f"\nSuccess! Stabilized cropped video saved as: {output_video}")
    except Exception as e:
        print(f"Error processing video: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
