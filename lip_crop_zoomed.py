#!/usr/bin/env python3
"""
Zoomed-in lip cropping script - makes lips fill most of the 96x64 frame.
Based on the successful bbaf2n_lips_centered.mp4 approach but with closer crop.
"""

import cv2
import numpy as np
import os
import sys
from pathlib import Path

def detect_lips_by_color(frame):
    """
    Detect lips using color detection (pink/red hues).
    Returns the center coordinates of the detected lip region.
    """
    # Convert to HSV for better color detection
    hsv = frame.copy()
    hsv = cv2.cvtColor(hsv, cv2.COLOR_BGR2HSV)
    
    # Define range for pink/red lip colors
    # Lower range - darker reds/pinks
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    
    # Upper range - lighter reds/pinks  
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    
    # Create masks for both red ranges
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    
    # Combine masks
    mask = cv2.bitwise_or(mask1, mask2)
    
    # Also try pink range
    lower_pink = np.array([140, 50, 50])
    upper_pink = np.array([170, 255, 255])
    mask_pink = cv2.inRange(hsv, lower_pink, upper_pink)
    
    # Combine all masks
    final_mask = cv2.bitwise_or(mask, mask_pink)
    
    # Clean up the mask
    kernel = np.ones((3,3), np.uint8)
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel)
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
    
    # Find contours
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None, None
    
    # Focus on the lower half of the frame (where lips typically are)
    frame_height = frame.shape[0]
    lower_half_y = frame_height // 3
    
    # Filter contours in the lower portion and by size
    lip_contours = []
    for contour in contours:
        # Get bounding box
        x, y, w, h = cv2.boundingRect(contour)
        area = cv2.contourArea(contour)
        
        # Filter: must be in lower 2/3 of frame and reasonable size
        if y > lower_half_y and 100 < area < 5000 and w > 10 and h > 5:
            lip_contours.append(contour)
    
    if not lip_contours:
        return None, None
    
    # Use the largest qualifying contour as lips
    largest_contour = max(lip_contours, key=cv2.contourArea)
    
    # Get center of the contour
    M = cv2.moments(largest_contour)
    if M["m00"] != 0:
        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])
        return center_x, center_y
    
    return None, None

def find_best_lip_position(video_path, num_samples=10):
    """
    Sample multiple frames to find the best lip position.
    Returns the most consistent lip center position.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Sample frames evenly throughout the video
    sample_frames = np.linspace(10, total_frames-10, num_samples, dtype=int)
    
    detected_positions = []
    
    print("Analyzing frames to find optimal lip position...")
    
    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            lip_x, lip_y = detect_lips_by_color(frame)
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Found lips at ({lip_x}, {lip_y})")
    
    cap.release()
    
    if not detected_positions:
        print("Could not detect lips in any sample frames!")
        return None, None
    
    # Use median position for stability
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]
    
    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))
    
    print(f"Selected lip center position: ({median_x}, {median_y})")
    print(f"Based on {len(detected_positions)} successful detections")
    
    return median_x, median_y

def crop_lips_zoomed_centered(frame, lip_center_x, lip_center_y, width=96, height=64, zoom_factor=0.6):
    """
    Crop frame with lips centered but ZOOMED IN to fill more of the frame.
    zoom_factor: 0.6 means crop a smaller region (60% of normal size) then scale up to 96x64
    This makes the lips appear larger in the final output.
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Calculate the actual crop size (smaller than target for zoom effect)
    crop_width = int(width * zoom_factor)   # e.g., 96 * 0.6 = 57.6 -> 58
    crop_height = int(height * zoom_factor) # e.g., 64 * 0.6 = 38.4 -> 38
    
    print(f"DEBUG: Zooming with factor {zoom_factor}")
    print(f"DEBUG: Crop size: {crop_width}x{crop_height} (will be scaled to {width}x{height})")
    print(f"DEBUG: Lip position: ({lip_center_x}, {lip_center_y})")
    
    # Calculate crop boundaries with lips at center of the smaller crop
    half_crop_w = crop_width // 2
    half_crop_h = crop_height // 2
    
    crop_left = lip_center_x - half_crop_w
    crop_right = lip_center_x + half_crop_w
    crop_top = lip_center_y - half_crop_h
    crop_bottom = lip_center_y + half_crop_h
    
    print(f"DEBUG: Calculated crop region: ({crop_left}, {crop_top}) to ({crop_right}, {crop_bottom})")
    
    # Ensure we don't go outside the original frame boundaries
    actual_left = max(0, crop_left)
    actual_right = min(frame_w, crop_right)
    actual_top = max(0, crop_top)
    actual_bottom = min(frame_h, crop_bottom)
    
    print(f"DEBUG: Adjusted crop region: ({actual_left}, {actual_top}) to ({actual_right}, {actual_bottom})")
    
    # Crop the region
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_height, actual_width = cropped.shape[:2]
    
    print(f"DEBUG: Cropped region size: {actual_width}x{actual_height}")
    
    # Scale up the cropped region to the target size (this creates the zoom effect)
    zoomed = cv2.resize(cropped, (width, height))
    
    print(f"DEBUG: Final output size: {zoomed.shape[1]}x{zoomed.shape[0]}")
    
    return zoomed

def process_video_zoomed(input_path, output_path, target_width=96, target_height=64, zoom_factor=0.6):
    """
    Process the video with zoomed-in lips that fill more of the frame.
    """
    # First, find the best lip position by analyzing sample frames
    lip_center_x, lip_center_y = find_best_lip_position(input_path)
    
    if lip_center_x is None or lip_center_y is None:
        print("Could not detect lips! Falling back to center of frame...")
        # Open video to get frame dimensions
        cap = cv2.VideoCapture(input_path)
        ret, frame = cap.read()
        if ret:
            lip_center_x = frame.shape[1] // 2
            lip_center_y = int(frame.shape[0] * 0.7)  # Lower portion of frame
        cap.release()
        
        if lip_center_x is None:
            raise ValueError("Could not process video")
    
    # Now process the entire video using this FIXED position with zoom
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        raise ValueError(f"Could not open video file: {input_path}")
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"\nProcessing video: {input_path}")
    print(f"FPS: {fps}, Total frames: {total_frames}")
    print(f"Target dimensions: {target_width}x{target_height}")
    print(f"Zoom factor: {zoom_factor} (smaller = more zoomed in)")
    print(f"Using FIXED crop position: ({lip_center_x}, {lip_center_y}) - NO SHAKINESS!")
    
    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (target_width, target_height))
    
    frame_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Use the SAME fixed position for every single frame with ZOOM
        if frame_count == 1:
            print(f"\nDEBUG INFO FOR FIRST FRAME:")
            cropped_frame = crop_lips_zoomed_centered(frame, lip_center_x, lip_center_y, target_width, target_height, zoom_factor)
        else:
            # No debug for other frames
            cropped_frame = cv2.resize(
                frame[lip_center_y - int(target_height * zoom_factor)//2:lip_center_y + int(target_height * zoom_factor)//2,
                      lip_center_x - int(target_width * zoom_factor)//2:lip_center_x + int(target_width * zoom_factor)//2],
                (target_width, target_height)
            )
        
        # Write frame
        out.write(cropped_frame)
        
        # Progress indicator
        if frame_count % 30 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames} frames)")
    
    # Cleanup
    cap.release()
    out.release()
    cv2.destroyAllWindows()
    
    print(f"Processing complete! Output saved to: {output_path}")
    print("Video should have lips filling most of the frame!")

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    
    # Create versions with different zoom levels
    zoom_levels = [
        (0.6, "zoom60"),   # Crop 60% of normal size = 1.67x zoom
        (0.5, "zoom50"),   # Crop 50% of normal size = 2x zoom  
        (0.7, "zoom70"),   # Crop 70% of normal size = 1.43x zoom
        (0.4, "zoom40"),   # Crop 40% of normal size = 2.5x zoom
    ]
    
    for zoom_factor, suffix in zoom_levels:
        output_video = f"/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n_lips_zoomed_{suffix}.mp4"
        
        print(f"\n{'='*60}")
        print(f"Creating zoomed version with factor {zoom_factor}")
        
        try:
            process_video_zoomed(input_video, output_video, zoom_factor=zoom_factor)
            print(f"✅ Success! Created: bbaf2n_lips_zoomed_{suffix}.mp4")
        except Exception as e:
            print(f"❌ Error: {str(e)}")
        
        # Create all zoom levels
        if zoom_factor == 0.6:  # Only continue after first success
            continue

if __name__ == "__main__":
    main()
