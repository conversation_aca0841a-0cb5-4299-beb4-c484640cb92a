#!/usr/bin/env python3
"""
Check the doctor video to see what we're working with.
"""

import cv2
import numpy as np

def analyze_doctor_video():
    video_path = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"Could not open video: {video_path}")
        return
    
    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"Video properties:")
    print(f"- Resolution: {width}x{height}")
    print(f"- FPS: {fps}")
    print(f"- Total frames: {total_frames}")
    
    # Load face cascade
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    # Check several frames
    sample_frames = [10, 20, 30, 40, 50] if total_frames > 50 else [5, 10, 15, 20, 25]
    
    for frame_num in sample_frames:
        if frame_num >= total_frames:
            continue
            
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            print(f"\nFrame {frame_num}:")
            print(f"- Frame size: {frame.shape[1]}x{frame.shape[0]}")
            
            # Try face detection with different parameters
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Try different scale factors and min neighbors
            for scale_factor in [1.1, 1.2, 1.3, 1.5]:
                for min_neighbors in [3, 4, 5, 6]:
                    faces = face_cascade.detectMultiScale(gray, scale_factor, min_neighbors)
                    if len(faces) > 0:
                        print(f"  Found {len(faces)} faces with scale={scale_factor}, neighbors={min_neighbors}")
                        for i, (x, y, w, h) in enumerate(faces):
                            print(f"    Face {i}: ({x}, {y}) size {w}x{h}")
                        break
                if len(faces) > 0:
                    break
            
            if len(faces) == 0:
                print("  No faces detected with any parameters")
                
                # Save a sample frame for manual inspection
                if frame_num == sample_frames[0]:
                    cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/doctor_sample_frame.jpg", frame)
                    print(f"  Saved sample frame as doctor_sample_frame.jpg")
    
    cap.release()

if __name__ == "__main__":
    analyze_doctor_video()
