#!/usr/bin/env python3
"""
Inspect the doctor video to see what we're actually working with.
Save sample frames and show detection results visually.
"""

import cv2
import numpy as np

def save_sample_frames_with_detection():
    """
    Save sample frames from the doctor video with detection overlays.
    """
    video_path = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"Could not open video: {video_path}")
        return
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"Video has {total_frames} frames, resolution: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")
    
    # Save frames from different parts of the video
    sample_frames = [10, 20, 30, 40, 50, 60, 70] if total_frames > 70 else [5, 15, 25, 35, 45]
    
    for frame_num in sample_frames:
        if frame_num >= total_frames:
            continue
            
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            # Save original frame
            cv2.imwrite(f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_frame_{frame_num}_original.jpg", frame)
            
            # Try face detection
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Try different parameters
            for scale_factor in [1.1, 1.2, 1.3]:
                for min_neighbors in [3, 4, 5]:
                    faces = face_cascade.detectMultiScale(gray, scale_factor, min_neighbors)
                    if len(faces) > 0:
                        frame_with_faces = frame.copy()
                        for (x, y, w, h) in faces:
                            cv2.rectangle(frame_with_faces, (x, y), (x+w, y+h), (0, 255, 0), 2)
                            # Mark estimated lip position
                            lip_x = x + w // 2
                            lip_y = y + int(h * 0.8)
                            cv2.circle(frame_with_faces, (lip_x, lip_y), 5, (0, 0, 255), -1)
                            cv2.putText(frame_with_faces, f"Face: {x},{y} {w}x{h}", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                            cv2.putText(frame_with_faces, f"Est Lips: {lip_x},{lip_y}", (lip_x-30, lip_y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)
                        
                        cv2.imwrite(f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_frame_{frame_num}_faces_s{scale_factor}_n{min_neighbors}.jpg", frame_with_faces)
                        print(f"Frame {frame_num}: Found {len(faces)} faces with scale={scale_factor}, neighbors={min_neighbors}")
                        break
                if len(faces) > 0:
                    break
            
            # Try color detection for lips
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            
            # Multiple lip color ranges
            lip_ranges = [
                ([0, 50, 50], [10, 255, 255]),      # Light red/pink
                ([160, 50, 50], [180, 255, 255]),   # Red/pink (wrapping around)
                ([0, 70, 70], [15, 255, 200]),      # Red
                ([165, 70, 70], [180, 255, 200]),   # Dark red
                ([0, 30, 100], [20, 150, 255]),     # Very light pink
                ([160, 30, 100], [180, 150, 255]),  # Light pink (wrap)
            ]
            
            combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            for lower, upper in lip_ranges:
                lower = np.array(lower)
                upper = np.array(upper)
                mask = cv2.inRange(hsv, lower, upper)
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # Save color mask
            cv2.imwrite(f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_frame_{frame_num}_color_mask.jpg", combined_mask)
            
            # Find contours and mark them
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            frame_with_colors = frame.copy()
            
            valid_regions = []
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                if area < 20:
                    continue
                    
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                
                # Draw all detected regions
                color = (255, 0, 0) if area > 50 and 0.8 < aspect_ratio < 4 else (0, 255, 255)
                cv2.rectangle(frame_with_colors, (x, y), (x+w, y+h), color, 2)
                cv2.putText(frame_with_colors, f"A:{int(area)} R:{aspect_ratio:.1f}", (x, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
                
                if area > 50 and 0.8 < aspect_ratio < 4:
                    center_x = x + w // 2
                    center_y = y + h // 2
                    cv2.circle(frame_with_colors, (center_x, center_y), 3, (0, 0, 255), -1)
                    valid_regions.append((area, center_x, center_y))
            
            if valid_regions:
                # Mark the largest valid region
                largest = max(valid_regions, key=lambda x: x[0])
                cv2.circle(frame_with_colors, (largest[1], largest[2]), 8, (0, 255, 0), 2)
                cv2.putText(frame_with_colors, f"BEST: {largest[1]},{largest[2]}", (largest[1]-30, largest[2]-15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
                print(f"Frame {frame_num}: Best color region at ({largest[1]}, {largest[2]}) with area {largest[0]}")
            
            cv2.imwrite(f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_frame_{frame_num}_color_detection.jpg", frame_with_colors)
    
    cap.release()
    print("\nSample frames saved! Check the files:")
    print("- *_original.jpg - Raw frames")
    print("- *_faces_*.jpg - Face detection results")
    print("- *_color_mask.jpg - Color detection masks")
    print("- *_color_detection.jpg - Color detection with annotations")

if __name__ == "__main__":
    save_sample_frames_with_detection()
