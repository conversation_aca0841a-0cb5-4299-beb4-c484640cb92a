#!/usr/bin/env python3
"""
Guaranteed center lip cropping - mathematically ensures lips are at exact center (48, 32).
"""

import cv2
import numpy as np
import os
import sys

def find_face_and_estimate_lips(frame):
    """
    Use face detection to find the face, then estimate lip position in lower part of face.
    Uses more relaxed parameters for better detection.
    """
    # Load face cascade
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Try multiple detection parameters
    detection_params = [
        (1.1, 3),  # Most relaxed
        (1.2, 3),
        (1.1, 4),
        (1.3, 4),
        (1.2, 4),
        (1.3, 5),  # Original parameters
    ]

    faces = []
    for scale_factor, min_neighbors in detection_params:
        faces = face_cascade.detectMultiScale(gray, scale_factor, min_neighbors)
        if len(faces) > 0:
            print(f"DEBUG: Found {len(faces)} faces with scale={scale_factor}, neighbors={min_neighbors}")
            break

    if len(faces) == 0:
        return None, None

    # Use the largest face
    face = max(faces, key=lambda x: x[2] * x[3])
    x, y, w, h = face

    print(f"DEBUG: Selected face: ({x}, {y}) size {w}x{h}")

    # Estimate lip position as lower center of face
    lip_x = x + w // 2
    lip_y = y + int(h * 0.8)  # 80% down from top of face

    return lip_x, lip_y

def find_optimal_lip_position_manual(video_path):
    """
    Find lip position using face detection and manual estimation.
    Uses more comprehensive frame sampling.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"Video has {total_frames} frames")

    # More comprehensive sampling - check many frames
    if total_frames > 50:
        sample_frames = list(range(10, min(total_frames-10, 70), 5))  # Every 5th frame from 10 to 70
    else:
        sample_frames = list(range(5, total_frames-5, 2))  # Every 2nd frame

    detected_positions = []

    print("Finding faces and estimating lip positions...")
    print(f"Checking frames: {sample_frames}")

    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()

        if ret:
            lip_x, lip_y = find_face_and_estimate_lips(frame)
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Estimated lips at ({lip_x}, {lip_y})")
            else:
                print(f"Frame {frame_num}: No face detected")

    cap.release()

    if not detected_positions:
        print("Could not detect faces in any frames!")
        return None, None

    print(f"Successfully detected faces in {len(detected_positions)} frames")

    # Use median position
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]

    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))

    print(f"Estimated lip center: ({median_x}, {median_y})")
    return median_x, median_y

def crop_with_guaranteed_center(frame, lip_x, lip_y, output_width=96, output_height=64, zoom_factor=0.66):
    """
    GUARANTEED to put lips at exact center (48, 32) of 96x64 output.
    
    Logic:
    1. Calculate crop size based on zoom factor
    2. Crop region centered on lip position
    3. If crop is perfect size, lips will be at center when scaled
    4. If crop hits boundaries, pad with black to maintain centering
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Target center in output
    target_center_x = output_width // 2   # 48
    target_center_y = output_height // 2  # 32
    
    # Calculate crop dimensions
    crop_width = int(output_width * zoom_factor)
    crop_height = int(output_height * zoom_factor)
    
    print(f"DEBUG: Target center in output: ({target_center_x}, {target_center_y})")
    print(f"DEBUG: Crop size: {crop_width}x{crop_height}")
    print(f"DEBUG: Lip position in source: ({lip_x}, {lip_y})")
    
    # Calculate ideal crop boundaries (centered on lips)
    ideal_left = lip_x - crop_width // 2
    ideal_right = lip_x + crop_width // 2
    ideal_top = lip_y - crop_height // 2
    ideal_bottom = lip_y + crop_height // 2
    
    print(f"DEBUG: Ideal crop region: ({ideal_left}, {ideal_top}) to ({ideal_right}, {ideal_bottom})")
    
    # Calculate actual crop boundaries (within frame limits)
    actual_left = max(0, ideal_left)
    actual_right = min(frame_w, ideal_right)
    actual_top = max(0, ideal_top)
    actual_bottom = min(frame_h, ideal_bottom)
    
    print(f"DEBUG: Actual crop region: ({actual_left}, {actual_top}) to ({actual_right}, {actual_bottom})")
    
    # Crop the available region
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_crop_h, actual_crop_w = cropped.shape[:2]
    
    print(f"DEBUG: Actual cropped size: {actual_crop_w}x{actual_crop_h}")
    
    # Create output canvas
    output_canvas = np.zeros((output_height, output_width, 3), dtype=np.uint8)
    
    if actual_crop_w == crop_width and actual_crop_h == crop_height:
        # Perfect crop - just resize and lips will be centered
        print("DEBUG: Perfect crop - resizing directly")
        resized = cv2.resize(cropped, (output_width, output_height))
        return resized
    else:
        # Imperfect crop due to boundaries - need to maintain lip centering
        print("DEBUG: Imperfect crop - maintaining lip centering")
        
        # Calculate where the lip appears in the cropped region
        lip_in_crop_x = lip_x - actual_left
        lip_in_crop_y = lip_y - actual_top
        
        print(f"DEBUG: Lip position in cropped region: ({lip_in_crop_x}, {lip_in_crop_y})")
        
        # Scale the cropped region
        scale_x = output_width / actual_crop_w
        scale_y = output_height / actual_crop_h
        
        # Use uniform scaling to maintain aspect ratio
        scale = min(scale_x, scale_y)
        
        scaled_width = int(actual_crop_w * scale)
        scaled_height = int(actual_crop_h * scale)
        
        scaled_cropped = cv2.resize(cropped, (scaled_width, scaled_height))
        
        print(f"DEBUG: Scaled cropped size: {scaled_width}x{scaled_height}")
        
        # Calculate where lip will be in scaled image
        lip_in_scaled_x = int(lip_in_crop_x * scale)
        lip_in_scaled_y = int(lip_in_crop_y * scale)
        
        print(f"DEBUG: Lip position in scaled image: ({lip_in_scaled_x}, {lip_in_scaled_y})")
        
        # Calculate paste position to center the lips
        paste_x = target_center_x - lip_in_scaled_x
        paste_y = target_center_y - lip_in_scaled_y
        
        print(f"DEBUG: Paste position: ({paste_x}, {paste_y})")
        
        # Ensure paste position is valid
        paste_x = max(0, min(paste_x, output_width - scaled_width))
        paste_y = max(0, min(paste_y, output_height - scaled_height))
        
        print(f"DEBUG: Adjusted paste position: ({paste_x}, {paste_y})")
        
        # Paste the scaled image
        output_canvas[paste_y:paste_y + scaled_height, paste_x:paste_x + scaled_width] = scaled_cropped
        
        return output_canvas

def crop_with_guaranteed_center_conditional_debug(frame, lip_x, lip_y, output_width=96, output_height=64, zoom_factor=0.66, show_debug=False):
    """
    Same as crop_with_guaranteed_center but with conditional debug output.
    """
    frame_h, frame_w = frame.shape[:2]

    # Target center in output
    target_center_x = output_width // 2   # 48
    target_center_y = output_height // 2  # 32

    # Calculate crop dimensions
    crop_width = int(output_width * zoom_factor)
    crop_height = int(output_height * zoom_factor)

    if show_debug:
        print(f"DEBUG: Target center in output: ({target_center_x}, {target_center_y})")
        print(f"DEBUG: Crop size: {crop_width}x{crop_height}")
        print(f"DEBUG: Lip position in source: ({lip_x}, {lip_y})")

    # Calculate ideal crop boundaries (centered on lips)
    ideal_left = lip_x - crop_width // 2
    ideal_right = lip_x + crop_width // 2
    ideal_top = lip_y - crop_height // 2
    ideal_bottom = lip_y + crop_height // 2

    # Calculate actual crop boundaries (within frame limits)
    actual_left = max(0, ideal_left)
    actual_right = min(frame_w, ideal_right)
    actual_top = max(0, ideal_top)
    actual_bottom = min(frame_h, ideal_bottom)

    # Crop the available region
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_crop_h, actual_crop_w = cropped.shape[:2]

    if actual_crop_w == crop_width and actual_crop_h == crop_height:
        # Perfect crop - just resize and lips will be centered
        if show_debug:
            print("DEBUG: Perfect crop - resizing directly")
        resized = cv2.resize(cropped, (output_width, output_height))
        return resized
    else:
        # Imperfect crop due to boundaries - need to maintain lip centering
        if show_debug:
            print("DEBUG: Imperfect crop - maintaining lip centering")

        # Create output canvas
        output_canvas = np.zeros((output_height, output_width, 3), dtype=np.uint8)

        # Calculate where the lip appears in the cropped region
        lip_in_crop_x = lip_x - actual_left
        lip_in_crop_y = lip_y - actual_top

        # Scale the cropped region
        scale_x = output_width / actual_crop_w
        scale_y = output_height / actual_crop_h
        scale = min(scale_x, scale_y)

        scaled_width = int(actual_crop_w * scale)
        scaled_height = int(actual_crop_h * scale)
        scaled_cropped = cv2.resize(cropped, (scaled_width, scaled_height))

        # Calculate where lip will be in scaled image
        lip_in_scaled_x = int(lip_in_crop_x * scale)
        lip_in_scaled_y = int(lip_in_crop_y * scale)

        # Calculate paste position to center the lips
        paste_x = target_center_x - lip_in_scaled_x
        paste_y = target_center_y - lip_in_scaled_y

        # Ensure paste position is valid
        paste_x = max(0, min(paste_x, output_width - scaled_width))
        paste_y = max(0, min(paste_y, output_height - scaled_height))

        if show_debug:
            print(f"DEBUG: Final lip position will be: ({lip_in_scaled_x + paste_x}, {lip_in_scaled_y + paste_y})")

        # Paste the scaled image
        output_canvas[paste_y:paste_y + scaled_height, paste_x:paste_x + scaled_width] = scaled_cropped

        return output_canvas

def process_video_guaranteed_center(input_path, output_path, y_offset=15, zoom_factor=0.66):
    """
    Process video with guaranteed lip centering.
    """
    # Find base lip position using face detection
    base_lip_x, base_lip_y = find_optimal_lip_position_manual(input_path)
    
    if base_lip_x is None:
        print("Could not detect faces!")
        return False
    
    # Apply manual offset to target actual lips
    adjusted_lip_x = base_lip_x
    adjusted_lip_y = base_lip_y + y_offset
    
    print(f"Base lip estimate: ({base_lip_x}, {base_lip_y})")
    print(f"Y offset: {y_offset}")
    print(f"Final lip position: ({adjusted_lip_x}, {adjusted_lip_y})")
    print(f"Zoom factor: {zoom_factor}")
    print("GUARANTEED: Lips will be at exact center (48, 32) in output")
    
    # Process video
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    print(f"\nProcessing {total_frames} frames...")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Show debug info only for first frame
        if frame_count == 1:
            print(f"\nDEBUG INFO FOR FIRST FRAME:")

        # Process frame (debug output only for first frame)
        cropped = crop_with_guaranteed_center_conditional_debug(frame, adjusted_lip_x, adjusted_lip_y, 96, 64, zoom_factor, frame_count == 1)
        
        out.write(cropped)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Complete! Saved: {output_path}")
    return True

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    output_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor_lips_guaranteed_center.mp4"

    print("Creating GUARANTEED centered lip video for doctor video...")
    print("Lips will be mathematically positioned at exact center (48, 32)")

    success = process_video_guaranteed_center(input_video, output_video, y_offset=15, zoom_factor=0.66)

    if success:
        print(f"\n✅ Success! Created: doctor_lips_guaranteed_center.mp4")
        print("Lips are GUARANTEED to be at exact center (48, 32) of 96x64 frame!")
    else:
        print(f"\n❌ Failed to create video")

if __name__ == "__main__":
    main()
