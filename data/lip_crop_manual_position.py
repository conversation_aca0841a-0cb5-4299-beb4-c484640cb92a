#!/usr/bin/env python3
"""
Manual lip positioning - you specify the exact coordinates where the lips are.
This is the most reliable method when automatic detection fails.
"""

import cv2
import numpy as np
import os
import sys

def crop_with_guaranteed_center_manual(frame, lip_x, lip_y, output_width=96, output_height=64, zoom_factor=0.66, show_debug=False):
    """
    Crop with lips guaranteed at center using manually specified position.
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Target center in output
    target_center_x = output_width // 2   # 48
    target_center_y = output_height // 2  # 32
    
    # Calculate crop dimensions
    crop_width = int(output_width * zoom_factor)
    crop_height = int(output_height * zoom_factor)
    
    if show_debug:
        print(f"DEBUG: Target center in output: ({target_center_x}, {target_center_y})")
        print(f"DEBUG: Crop size: {crop_width}x{crop_height}")
        print(f"DEBUG: Manual lip position: ({lip_x}, {lip_y})")
    
    # Calculate ideal crop boundaries (centered on lips)
    ideal_left = lip_x - crop_width // 2
    ideal_right = lip_x + crop_width // 2
    ideal_top = lip_y - crop_height // 2
    ideal_bottom = lip_y + crop_height // 2
    
    # Calculate actual crop boundaries (within frame limits)
    actual_left = max(0, ideal_left)
    actual_right = min(frame_w, ideal_right)
    actual_top = max(0, ideal_top)
    actual_bottom = min(frame_h, ideal_bottom)
    
    if show_debug:
        print(f"DEBUG: Ideal crop: ({ideal_left}, {ideal_top}) to ({ideal_right}, {ideal_bottom})")
        print(f"DEBUG: Actual crop: ({actual_left}, {actual_top}) to ({actual_right}, {actual_bottom})")
    
    # Crop the available region
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_crop_h, actual_crop_w = cropped.shape[:2]
    
    if actual_crop_w == crop_width and actual_crop_h == crop_height:
        # Perfect crop - just resize and lips will be centered
        if show_debug:
            print("DEBUG: Perfect crop - resizing directly")
        resized = cv2.resize(cropped, (output_width, output_height))
        return resized
    else:
        # Imperfect crop due to boundaries - need to maintain lip centering
        if show_debug:
            print("DEBUG: Imperfect crop - maintaining lip centering")
        
        # Create output canvas
        output_canvas = np.zeros((output_height, output_width, 3), dtype=np.uint8)
        
        # Calculate where the lip appears in the cropped region
        lip_in_crop_x = lip_x - actual_left
        lip_in_crop_y = lip_y - actual_top
        
        # Scale the cropped region
        scale_x = output_width / actual_crop_w
        scale_y = output_height / actual_crop_h
        scale = min(scale_x, scale_y)
        
        scaled_width = int(actual_crop_w * scale)
        scaled_height = int(actual_crop_h * scale)
        scaled_cropped = cv2.resize(cropped, (scaled_width, scaled_height))
        
        # Calculate where lip will be in scaled image
        lip_in_scaled_x = int(lip_in_crop_x * scale)
        lip_in_scaled_y = int(lip_in_crop_y * scale)
        
        # Calculate paste position to center the lips
        paste_x = target_center_x - lip_in_scaled_x
        paste_y = target_center_y - lip_in_scaled_y
        
        # Ensure paste position is valid
        paste_x = max(0, min(paste_x, output_width - scaled_width))
        paste_y = max(0, min(paste_y, output_height - scaled_height))
        
        if show_debug:
            print(f"DEBUG: Lip in crop: ({lip_in_crop_x}, {lip_in_crop_y})")
            print(f"DEBUG: Scale factor: {scale:.3f}")
            print(f"DEBUG: Scaled size: {scaled_width}x{scaled_height}")
            print(f"DEBUG: Lip in scaled: ({lip_in_scaled_x}, {lip_in_scaled_y})")
            print(f"DEBUG: Paste position: ({paste_x}, {paste_y})")
            print(f"DEBUG: Final lip position: ({lip_in_scaled_x + paste_x}, {lip_in_scaled_y + paste_y})")
        
        # Paste the scaled image
        output_canvas[paste_y:paste_y + scaled_height, paste_x:paste_x + scaled_width] = scaled_cropped
        
        return output_canvas

def process_video_manual_position(input_path, output_path, lip_x, lip_y, zoom_factor=0.66):
    """
    Process video using manually specified lip position.
    """
    print(f"Using MANUAL lip position: ({lip_x}, {lip_y})")
    print(f"Zoom factor: {zoom_factor}")
    print("GUARANTEED: Lips will be at exact center (48, 32) in output")
    
    # Process video
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    print(f"\nProcessing {total_frames} frames...")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Show debug info only for first frame
        if frame_count == 1:
            print(f"\nDEBUG INFO FOR FIRST FRAME:")
        
        # Process frame
        cropped = crop_with_guaranteed_center_manual(frame, lip_x, lip_y, 96, 64, zoom_factor, frame_count == 1)
        out.write(cropped)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Complete! Saved: {output_path}")
    return True

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    
    print("MANUAL LIP POSITIONING")
    print("======================")
    print("Please check the sample frames that were saved:")
    print("- doctor_frame_*_original.jpg")
    print("- doctor_frame_*_faces_*.jpg (if faces were detected)")
    print("- doctor_frame_*_color_detection.jpg")
    print()
    print("Look at these images and identify where the lips actually are.")
    print("The video is 400x200 pixels.")
    print()
    print("Based on the face detection, frame 40 found a face at (154, 84) size 102x102")
    print("This would put estimated lips at around (205, 165)")
    print()
    print("Let me try a few different manual positions:")
    
    # Try different manual positions based on the face detection
    test_positions = [
        (200, 160, "face_based_200_160"),
        (180, 140, "adjusted_180_140"), 
        (220, 150, "adjusted_220_150"),
        (200, 130, "higher_200_130"),
        (200, 180, "lower_200_180"),
    ]
    
    for lip_x, lip_y, suffix in test_positions:
        output_video = f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_lips_manual_{suffix}.mp4"
        print(f"\nTrying position ({lip_x}, {lip_y}) -> {suffix}.mp4")
        
        success = process_video_manual_position(input_video, output_video, lip_x, lip_y, zoom_factor=0.66)
        
        if success:
            print(f"✅ Created: doctor_lips_manual_{suffix}.mp4")
        else:
            print(f"❌ Failed to create video")
    
    print(f"\n🎯 DONE! Created 5 test videos with different lip positions.")
    print("Check each one to see which position looks best:")
    for _, _, suffix in test_positions:
        print(f"- doctor_lips_manual_{suffix}.mp4")

if __name__ == "__main__":
    main()
