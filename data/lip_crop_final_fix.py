#!/usr/bin/env python3
"""
Final fix for lip cropping - keeps lips in frame by adjusting crop position.
Based on the successful bbaf2n_lips_manual_down15_zoom60 approach.
"""

import cv2
import numpy as np
import os
import sys

def find_face_and_estimate_lips(frame):
    """
    Use face detection to find the face, then estimate lip position in lower part of face.
    """
    # Load face cascade
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.3, 5)
    
    if len(faces) == 0:
        return None, None
    
    # Use the largest face
    face = max(faces, key=lambda x: x[2] * x[3])
    x, y, w, h = face
    
    # Estimate lip position as lower center of face
    # Lips are typically at about 75-85% down from top of face
    lip_x = x + w // 2
    lip_y = y + int(h * 0.8)  # 80% down from top of face
    
    return lip_x, lip_y

def find_optimal_lip_position_manual(video_path):
    """
    Find lip position using face detection and manual estimation.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Sample frames throughout video
    sample_frames = np.linspace(10, total_frames-10, 10, dtype=int)
    
    detected_positions = []
    
    print("Finding faces and estimating lip positions...")
    
    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            lip_x, lip_y = find_face_and_estimate_lips(frame)
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Estimated lips at ({lip_x}, {lip_y})")
    
    cap.release()
    
    if not detected_positions:
        print("Could not detect faces in any frames!")
        return None, None
    
    # Use median position
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]
    
    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))
    
    print(f"Estimated lip center: ({median_x}, {median_y})")
    return median_x, median_y

def crop_lips_positioned(frame, lip_center_x, lip_center_y, width=96, height=64, zoom_factor=0.6, lip_position_y=0.4):
    """
    Crop with lips at a specific vertical position in the output frame.
    lip_position_y: 0.0 = top, 0.5 = center, 1.0 = bottom
    0.4 means lips will be at 40% down from top (closer to top, leaving room below)
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Calculate crop size for zoom effect
    crop_width = int(width * zoom_factor)
    crop_height = int(height * zoom_factor)
    
    # Calculate where lips should appear in the output frame
    target_lip_y_in_output = int(height * lip_position_y)
    
    # Calculate crop boundaries
    # We want lips to appear at target_lip_y_in_output when we scale crop_height to height
    # So in the crop, lips should be at: target_lip_y_in_output * (crop_height / height)
    target_lip_y_in_crop = int(target_lip_y_in_output * crop_height / height)
    
    # Calculate crop region
    crop_left = lip_center_x - crop_width // 2
    crop_right = crop_left + crop_width
    crop_top = lip_center_y - target_lip_y_in_crop
    crop_bottom = crop_top + crop_height
    
    print(f"DEBUG: Lip position in output will be at y={target_lip_y_in_output} (position {lip_position_y})")
    print(f"DEBUG: Crop region: ({crop_left}, {crop_top}) to ({crop_right}, {crop_bottom})")
    
    # Ensure boundaries
    actual_left = max(0, crop_left)
    actual_right = min(frame_w, crop_right)
    actual_top = max(0, crop_top)
    actual_bottom = min(frame_h, crop_bottom)
    
    # Crop and resize
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    
    if cropped.size == 0:
        # Fallback to black frame
        return np.zeros((height, width, 3), dtype=np.uint8)
    
    # Scale to target size
    zoomed = cv2.resize(cropped, (width, height))
    return zoomed

def process_video_positioned_lips(input_path, output_path, y_offset=15, zoom_factor=0.6, lip_position=0.4):
    """
    Process video with lips positioned at specific location in frame.
    y_offset: offset from face detection estimate
    lip_position: where lips appear vertically (0.0=top, 0.5=center, 1.0=bottom)
    """
    # Find base lip position using face detection
    base_lip_x, base_lip_y = find_optimal_lip_position_manual(input_path)
    
    if base_lip_x is None:
        print("Could not detect faces!")
        return False
    
    # Apply manual offset to target actual lips
    adjusted_lip_x = base_lip_x
    adjusted_lip_y = base_lip_y + y_offset
    
    print(f"Base lip estimate: ({base_lip_x}, {base_lip_y})")
    print(f"Y offset: {y_offset}")
    print(f"Final lip position: ({adjusted_lip_x}, {adjusted_lip_y})")
    print(f"Lips will be positioned at {lip_position*100}% down in output frame")
    
    # Process video
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    print(f"\nProcessing {total_frames} frames...")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Show debug info only for first frame
        if frame_count == 1:
            print(f"\nDEBUG INFO FOR FIRST FRAME:")
            cropped = crop_lips_positioned(frame, adjusted_lip_x, adjusted_lip_y, 96, 64, zoom_factor, lip_position)
        else:
            # No debug for other frames - use simplified version
            crop_width = int(96 * zoom_factor)
            crop_height = int(64 * zoom_factor)
            target_lip_y_in_crop = int(lip_position * crop_height)
            
            crop_left = max(0, adjusted_lip_x - crop_width // 2)
            crop_right = min(frame.shape[1], crop_left + crop_width)
            crop_top = max(0, adjusted_lip_y - target_lip_y_in_crop)
            crop_bottom = min(frame.shape[0], crop_top + crop_height)
            
            cropped_region = frame[crop_top:crop_bottom, crop_left:crop_right]
            if cropped_region.size > 0:
                cropped = cv2.resize(cropped_region, (96, 64))
            else:
                cropped = np.zeros((64, 96, 3), dtype=np.uint8)
        
        out.write(cropped)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Complete! Saved: {output_path}")
    return True

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    
    # Create versions with lips at different vertical positions
    # Using the successful parameters from bbaf2n_lips_manual_down15_zoom60
    # but adjusting where lips appear in the output frame
    
    versions = [
        (15, 0.6, 0.35, "pos35"),  # Lips at 35% down (higher up, more room below)
        (15, 0.6, 0.4, "pos40"),   # Lips at 40% down 
        (15, 0.6, 0.45, "pos45"),  # Lips at 45% down
        (15, 0.6, 0.3, "pos30"),   # Lips at 30% down (even higher)
        (15, 0.6, 0.25, "pos25"),  # Lips at 25% down (very high, max room below)
    ]
    
    for y_offset, zoom, lip_pos, suffix in versions:
        output_name = f"bbaf2n_lips_positioned_{suffix}_zoom{int(zoom*100)}.mp4"
        output_path = f"/Users/<USER>/Desktop/preprocessing_pipeline/{output_name}"
        
        print(f"\n{'='*60}")
        print(f"Creating version: Lips at {lip_pos*100}% down in frame")
        
        success = process_video_positioned_lips(input_video, output_path, y_offset, zoom, lip_pos)
        
        if success:
            print(f"✅ Created: {output_name}")
        else:
            print(f"❌ Failed: {output_name}")

if __name__ == "__main__":
    main()
