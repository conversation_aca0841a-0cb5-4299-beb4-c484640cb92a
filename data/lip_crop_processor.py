#!/usr/bin/env python3
"""
Video processing script to crop around lips region using color detection.
Detects lips once using pink/red color, then uses fixed crop position.
"""

import cv2
import numpy as np
import os
import sys
from pathlib import Path

def detect_lips_by_color(frame):
    """
    Detect lips using color detection (pink/red hues).
    Returns the center coordinates of the detected lip region.
    """
    # Convert to HSV for better color detection
    hsv = frame.copy()
    hsv = cv2.cvtColor(hsv, cv2.COLOR_BGR2HSV)

    # Define range for pink/red lip colors
    # Lower range - darker reds/pinks
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])

    # Upper range - lighter reds/pinks
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])

    # Create masks for both red ranges
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)

    # Combine masks
    mask = cv2.bitwise_or(mask1, mask2)

    # Also try pink range
    lower_pink = np.array([140, 50, 50])
    upper_pink = np.array([170, 255, 255])
    mask_pink = cv2.inRange(hsv, lower_pink, upper_pink)

    # Combine all masks
    final_mask = cv2.bitwise_or(mask, mask_pink)

    # Clean up the mask
    kernel = np.ones((3,3), np.uint8)
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel)
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)

    # Find contours
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        return None, None

    # Focus on the lower half of the frame (where lips typically are)
    frame_height = frame.shape[0]
    lower_half_y = frame_height // 3

    # Filter contours in the lower portion and by size
    lip_contours = []
    for contour in contours:
        # Get bounding box
        x, y, w, h = cv2.boundingRect(contour)
        area = cv2.contourArea(contour)

        # Filter: must be in lower 2/3 of frame and reasonable size
        if y > lower_half_y and 100 < area < 5000 and w > 10 and h > 5:
            lip_contours.append(contour)

    if not lip_contours:
        return None, None

    # Use the largest qualifying contour as lips
    largest_contour = max(lip_contours, key=cv2.contourArea)

    # Get center of the contour
    M = cv2.moments(largest_contour)
    if M["m00"] != 0:
        center_x = int(M["m10"] / M["m00"])
        center_y = int(M["m01"] / M["m00"])
        return center_x, center_y

    return None, None

def find_best_lip_position(video_path, num_samples=10):
    """
    Sample multiple frames to find the best lip position.
    Returns the most consistent lip center position.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # Sample frames evenly throughout the video
    sample_frames = np.linspace(10, total_frames-10, num_samples, dtype=int)

    detected_positions = []

    print("Analyzing frames to find optimal lip position...")

    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()

        if ret:
            lip_x, lip_y = detect_lips_by_color(frame)
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Found lips at ({lip_x}, {lip_y})")

    cap.release()

    if not detected_positions:
        print("Could not detect lips in any sample frames!")
        return None, None

    # Use median position for stability
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]

    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))

    print(f"Selected lip center position: ({median_x}, {median_y})")
    print(f"Based on {len(detected_positions)} successful detections")

    return median_x, median_y

def crop_with_lips_centered(frame, lip_center_x, lip_center_y, width=96, height=64):
    """
    Crop frame so that the detected lips appear at the EXACT CENTER of the output frame.

    Logic:
    - Output center is at (48, 32) for 96x64 frame
    - If lips are at (lip_x, lip_y) in original frame
    - We need to crop a region where lips end up at (48, 32) in output
    - So we crop from (lip_x - 48, lip_y - 32) to (lip_x + 48, lip_y + 32)
    """
    frame_h, frame_w = frame.shape[:2]

    # Target center position in output frame
    target_center_x = width // 2   # 48 for 96-wide frame
    target_center_y = height // 2  # 32 for 64-high frame

    print(f"DEBUG: Lip position in original frame: ({lip_center_x}, {lip_center_y})")
    print(f"DEBUG: Target center in output frame: ({target_center_x}, {target_center_y})")

    # Calculate crop boundaries
    # To center lips at (48, 32), we crop from (lip_x-48, lip_y-32) to (lip_x+48, lip_y+32)
    crop_left = lip_center_x - target_center_x
    crop_right = lip_center_x + target_center_x
    crop_top = lip_center_y - target_center_y
    crop_bottom = lip_center_y + target_center_y

    print(f"DEBUG: Calculated crop region: left={crop_left}, right={crop_right}, top={crop_top}, bottom={crop_bottom}")

    # Ensure we don't go outside the original frame boundaries
    actual_left = max(0, crop_left)
    actual_right = min(frame_w, crop_right)
    actual_top = max(0, crop_top)
    actual_bottom = min(frame_h, crop_bottom)

    print(f"DEBUG: Adjusted crop region: left={actual_left}, right={actual_right}, top={actual_top}, bottom={actual_bottom}")

    # Crop the region
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_height, actual_width = cropped.shape[:2]

    print(f"DEBUG: Cropped region size: {actual_width}x{actual_height}")

    # If we got the exact size we wanted, return it
    if actual_width == width and actual_height == height:
        print("DEBUG: Perfect crop - returning as-is")
        return cropped

    # Otherwise, we need to pad/adjust to get exact dimensions
    print("DEBUG: Need to pad/resize to get exact dimensions")

    # Create output frame filled with black
    output_frame = np.zeros((height, width, 3), dtype=np.uint8)

    # Calculate where to place the cropped region in the output frame
    # We want the lips to end up at the center, so we need to offset accordingly

    # How much did we miss from our ideal crop?
    left_deficit = actual_left - crop_left  # How much we couldn't crop from left
    top_deficit = actual_top - crop_top     # How much we couldn't crop from top
    right_deficit = crop_right - actual_right  # How much we couldn't crop from right
    bottom_deficit = crop_bottom - actual_bottom  # How much we couldn't crop from bottom

    # Place the cropped region in the output frame
    paste_left = left_deficit
    paste_top = top_deficit
    paste_right = paste_left + actual_width
    paste_bottom = paste_top + actual_height

    # Ensure we don't exceed output frame boundaries
    paste_left = max(0, min(paste_left, width - actual_width))
    paste_top = max(0, min(paste_top, height - actual_height))
    paste_right = paste_left + actual_width
    paste_bottom = paste_top + actual_height

    print(f"DEBUG: Pasting cropped region at: ({paste_left}, {paste_top}) to ({paste_right}, {paste_bottom})")

    # Place the cropped region
    if paste_right <= width and paste_bottom <= height:
        output_frame[paste_top:paste_bottom, paste_left:paste_right] = cropped
    else:
        # If still doesn't fit, resize the cropped region
        print("DEBUG: Resizing cropped region to fit")
        resized_cropped = cv2.resize(cropped, (width, height))
        return resized_cropped

    return output_frame

def crop_with_lips_centered_no_debug(frame, lip_center_x, lip_center_y, width=96, height=64):
    """
    Same as crop_with_lips_centered but without debug prints for performance.
    """
    frame_h, frame_w = frame.shape[:2]

    target_center_x = width // 2
    target_center_y = height // 2

    crop_left = lip_center_x - target_center_x
    crop_right = lip_center_x + target_center_x
    crop_top = lip_center_y - target_center_y
    crop_bottom = lip_center_y + target_center_y

    actual_left = max(0, crop_left)
    actual_right = min(frame_w, crop_right)
    actual_top = max(0, crop_top)
    actual_bottom = min(frame_h, crop_bottom)

    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_height, actual_width = cropped.shape[:2]

    if actual_width == width and actual_height == height:
        return cropped

    output_frame = np.zeros((height, width, 3), dtype=np.uint8)

    left_deficit = actual_left - crop_left
    top_deficit = actual_top - crop_top

    paste_left = left_deficit
    paste_top = top_deficit
    paste_right = paste_left + actual_width
    paste_bottom = paste_top + actual_height

    paste_left = max(0, min(paste_left, width - actual_width))
    paste_top = max(0, min(paste_top, height - actual_height))
    paste_right = paste_left + actual_width
    paste_bottom = paste_top + actual_height

    if paste_right <= width and paste_bottom <= height:
        output_frame[paste_top:paste_bottom, paste_left:paste_right] = cropped
    else:
        resized_cropped = cv2.resize(cropped, (width, height))
        return resized_cropped

    return output_frame

def estimate_lip_size_and_adjust_position(frame, lip_center_x, lip_center_y):
    """
    Analyze the lip region to estimate lip size and adjust position for better centering.
    """
    # Create a small region around detected lip center to analyze
    search_size = 40
    y_start = max(0, lip_center_y - search_size//2)
    y_end = min(frame.shape[0], lip_center_y + search_size//2)
    x_start = max(0, lip_center_x - search_size//2)
    x_end = min(frame.shape[1], lip_center_x + search_size//2)

    lip_region = frame[y_start:y_end, x_start:x_end]

    # Convert to HSV for better lip detection
    hsv_region = cv2.cvtColor(lip_region, cv2.COLOR_BGR2HSV)

    # Create mask for lip colors in this small region
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])

    mask1 = cv2.inRange(hsv_region, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv_region, lower_red2, upper_red2)
    mask = cv2.bitwise_or(mask1, mask2)

    # Find contours in this small region
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if contours:
        # Get the largest contour (should be the lips)
        largest_contour = max(contours, key=cv2.contourArea)

        # Get more precise center of the lip contour
        M = cv2.moments(largest_contour)
        if M["m00"] != 0:
            local_center_x = int(M["m10"] / M["m00"])
            local_center_y = int(M["m01"] / M["m00"])

            # Convert back to full frame coordinates
            adjusted_x = x_start + local_center_x
            adjusted_y = y_start + local_center_y

            return adjusted_x, adjusted_y

    # If no improvement found, return original position
    return lip_center_x, lip_center_y

def process_video_fixed_position(input_path, output_path, target_width=96, target_height=64):
    """
    Process the video using a FIXED crop position with lips perfectly centered.
    Detects lips once, then uses that position for the entire video.
    """
    # First, find the best lip position by analyzing sample frames
    lip_center_x, lip_center_y = find_best_lip_position(input_path)

    if lip_center_x is None or lip_center_y is None:
        print("Could not detect lips! Falling back to center of frame...")
        # Open video to get frame dimensions
        cap = cv2.VideoCapture(input_path)
        ret, frame = cap.read()
        if ret:
            lip_center_x = frame.shape[1] // 2
            lip_center_y = int(frame.shape[0] * 0.7)  # Lower portion of frame
        cap.release()

        if lip_center_x is None:
            raise ValueError("Could not process video")

    # Refine the lip position using a sample frame
    cap = cv2.VideoCapture(input_path)
    cap.set(cv2.CAP_PROP_POS_FRAMES, 20)  # Use frame 20 for refinement
    ret, sample_frame = cap.read()
    if ret:
        refined_x, refined_y = estimate_lip_size_and_adjust_position(sample_frame, lip_center_x, lip_center_y)
        lip_center_x, lip_center_y = refined_x, refined_y
        print(f"Refined lip position to: ({lip_center_x}, {lip_center_y})")
    cap.release()

    # Now process the entire video using this FIXED position
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        raise ValueError(f"Could not open video file: {input_path}")

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"\nProcessing video: {input_path}")
    print(f"FPS: {fps}, Total frames: {total_frames}")
    print(f"Target dimensions: {target_width}x{target_height}")
    print(f"Lips will be CENTERED in frame with 10-15% margins")
    print(f"Using FIXED crop position: ({lip_center_x}, {lip_center_y}) - NO SHAKINESS!")

    # Setup video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (target_width, target_height))

    frame_count = 0

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1

        # Use the SAME fixed position for every single frame with lips at CENTER
        # Only show debug info for first frame
        if frame_count == 1:
            print(f"\nDEBUG INFO FOR FIRST FRAME:")
            cropped_frame = crop_with_lips_centered(frame, lip_center_x, lip_center_y, target_width, target_height)
        else:
            # Disable debug prints for other frames
            cropped_frame = crop_with_lips_centered_no_debug(frame, lip_center_x, lip_center_y, target_width, target_height)

        # Write frame
        out.write(cropped_frame)

        # Progress indicator
        if frame_count % 30 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames} frames)")

    # Cleanup
    cap.release()
    out.release()
    cv2.destroyAllWindows()

    print(f"Processing complete! Output saved to: {output_path}")
    print("Video should be completely stable with lips centered and proper margins!")

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    output_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n_lips_debug_centered.mp4"

    # Check if input file exists
    if not os.path.exists(input_video):
        print(f"Error: Input video file not found: {input_video}")
        sys.exit(1)

    try:
        process_video_fixed_position(input_video, output_video, target_width=96, target_height=64)
        print(f"\nSuccess! Centered lips video saved as: {output_video}")
        print("This version has lips perfectly centered with proper margins!")
    except Exception as e:
        print(f"Error processing video: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
