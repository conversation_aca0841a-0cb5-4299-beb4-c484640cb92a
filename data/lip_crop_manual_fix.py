#!/usr/bin/env python3
"""
Manual fix for lip cropping - adjusts detection to target actual lips, not nose.
"""

import cv2
import numpy as np
import os
import sys

def find_face_and_estimate_lips(frame):
    """
    Use face detection to find the face, then estimate lip position in lower part of face.
    """
    # Load face cascade
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.3, 5)
    
    if len(faces) == 0:
        return None, None
    
    # Use the largest face
    face = max(faces, key=lambda x: x[2] * x[3])
    x, y, w, h = face
    
    # Estimate lip position as lower center of face
    # Lips are typically at about 75-85% down from top of face
    lip_x = x + w // 2
    lip_y = y + int(h * 0.8)  # 80% down from top of face
    
    return lip_x, lip_y

def find_optimal_lip_position_manual(video_path):
    """
    Find lip position using face detection and manual estimation.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Sample frames throughout video
    sample_frames = np.linspace(10, total_frames-10, 10, dtype=int)
    
    detected_positions = []
    
    print("Finding faces and estimating lip positions...")
    
    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            lip_x, lip_y = find_face_and_estimate_lips(frame)
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Estimated lips at ({lip_x}, {lip_y})")
    
    cap.release()
    
    if not detected_positions:
        print("Could not detect faces in any frames!")
        return None, None
    
    # Use median position
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]
    
    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))
    
    print(f"Estimated lip center: ({median_x}, {median_y})")
    return median_x, median_y

def crop_lips_zoomed_fixed(frame, lip_center_x, lip_center_y, width=96, height=64, zoom_factor=0.6):
    """
    Crop with lips centered and zoomed, with manual position adjustment.
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Calculate crop size for zoom effect
    crop_width = int(width * zoom_factor)
    crop_height = int(height * zoom_factor)
    
    # Calculate crop boundaries
    half_crop_w = crop_width // 2
    half_crop_h = crop_height // 2
    
    crop_left = lip_center_x - half_crop_w
    crop_right = lip_center_x + half_crop_w
    crop_top = lip_center_y - half_crop_h
    crop_bottom = lip_center_y + half_crop_h
    
    # Ensure boundaries
    actual_left = max(0, crop_left)
    actual_right = min(frame_w, crop_right)
    actual_top = max(0, crop_top)
    actual_bottom = min(frame_h, crop_bottom)
    
    # Crop and resize
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    
    if cropped.size == 0:
        # Fallback to black frame
        return np.zeros((height, width, 3), dtype=np.uint8)
    
    # Scale to target size
    zoomed = cv2.resize(cropped, (width, height))
    return zoomed

def process_video_manual_lips(input_path, output_path, y_offset=0, zoom_factor=0.6):
    """
    Process video with manual lip position adjustment.
    y_offset: positive moves detection DOWN (towards actual lips), negative moves UP
    """
    # Find base lip position using face detection
    base_lip_x, base_lip_y = find_optimal_lip_position_manual(input_path)
    
    if base_lip_x is None:
        print("Could not detect faces!")
        return False
    
    # Apply manual offset to target actual lips
    adjusted_lip_x = base_lip_x
    adjusted_lip_y = base_lip_y + y_offset
    
    print(f"Base lip estimate: ({base_lip_x}, {base_lip_y})")
    print(f"Y offset: {y_offset}")
    print(f"Final lip position: ({adjusted_lip_x}, {adjusted_lip_y})")
    
    # Process video
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    print(f"\nProcessing {total_frames} frames with zoom factor {zoom_factor}...")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Crop with fixed position and zoom
        cropped = crop_lips_zoomed_fixed(frame, adjusted_lip_x, adjusted_lip_y, 96, 64, zoom_factor)
        out.write(cropped)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Complete! Saved: {output_path}")
    return True

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    
    # Try different Y offsets to find the actual lips
    # Positive values move the detection DOWN from the estimated position
    offsets_to_try = [
        (15, "down15", 0.6),   # Move 15 pixels down from face estimate
        (25, "down25", 0.6),   # Move 25 pixels down
        (35, "down35", 0.6),   # Move 35 pixels down  
        (20, "down20", 0.5),   # Move 20 pixels down with more zoom
        (30, "down30", 0.5),   # Move 30 pixels down with more zoom
    ]
    
    for y_offset, suffix, zoom in offsets_to_try:
        output_name = f"bbaf2n_lips_manual_{suffix}_zoom{int(zoom*100)}.mp4"
        output_path = f"/Users/<USER>/Desktop/preprocessing_pipeline/{output_name}"
        
        print(f"\n{'='*60}")
        print(f"Creating version: Y offset +{y_offset}, zoom {zoom}")
        
        success = process_video_manual_lips(input_video, output_path, y_offset, zoom)
        
        if success:
            print(f"✅ Created: {output_name}")
        else:
            print(f"❌ Failed: {output_name}")

if __name__ == "__main__":
    main()
