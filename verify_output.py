#!/usr/bin/env python3
"""
Verify the output video to see if lips are actually centered.
"""

import cv2
import numpy as np

def verify_centering():
    # Check the latest output video
    video_path = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n_lips_debug_centered.mp4"
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"Could not open video: {video_path}")
        return
    
    # Get a few sample frames
    frame_numbers = [0, 10, 20, 30, 40]
    
    for i, frame_num in enumerate(frame_numbers):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            height, width = frame.shape[:2]
            print(f"Frame {frame_num}: Size = {width}x{height}")
            
            # Add center markers
            center_x, center_y = width // 2, height // 2
            marked_frame = frame.copy()
            
            # Draw crosshairs
            cv2.line(marked_frame, (0, center_y), (width-1, center_y), (0, 255, 0), 1)
            cv2.line(marked_frame, (center_x, 0), (center_x, height-1), (0, 255, 0), 1)
            cv2.circle(marked_frame, (center_x, center_y), 2, (0, 255, 0), -1)
            
            # Save marked frame
            output_path = f"/Users/<USER>/Desktop/preprocessing_pipeline/verify_frame_{frame_num}.jpg"
            cv2.imwrite(output_path, marked_frame)
            print(f"Saved: verify_frame_{frame_num}.jpg")
    
    cap.release()
    print("\nCheck the verify_frame_*.jpg files to see if lips are at the green crosshairs (center)")

if __name__ == "__main__":
    verify_centering()
