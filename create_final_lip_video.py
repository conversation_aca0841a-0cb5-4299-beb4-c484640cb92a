#!/usr/bin/env python3
"""
Create the final lip video once you've identified the correct position.
"""

import cv2
import numpy as np
import sys

def create_speaking_lips_video(input_path, output_path, lip_x, lip_y, zoom_factor=0.7):
    """
    Create video with lips centered, optimized for speaking motion.
    Uses slightly more zoom out to ensure lips don't exit frame during speech.
    """
    print(f"Creating speaking lips video:")
    print(f"- Lip position: ({lip_x}, {lip_y})")
    print(f"- Zoom factor: {zoom_factor} (extra room for speech movement)")
    print(f"- Output size: 96x64 pixels")
    
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        print(f"Error: Could not open video {input_path}")
        return False
    
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"- Input: {frame_width}x{frame_height}, {fps} FPS, {total_frames} frames")
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    # Calculate crop size - more room for speaking motion
    crop_width = int(96 * zoom_factor)
    crop_height = int(64 * zoom_factor)
    
    print(f"- Crop size: {crop_width}x{crop_height} -> scaled to 96x64")
    
    # Calculate crop boundaries
    left = max(0, lip_x - crop_width // 2)
    right = min(frame_width, left + crop_width)
    top = max(0, lip_y - crop_height // 2)
    bottom = min(frame_height, top + crop_height)
    
    # Adjust if we hit boundaries
    if right - left < crop_width:
        if left == 0:
            right = min(frame_width, crop_width)
        else:
            left = max(0, frame_width - crop_width)
            right = frame_width
    
    if bottom - top < crop_height:
        if top == 0:
            bottom = min(frame_height, crop_height)
        else:
            top = max(0, frame_height - crop_height)
            bottom = frame_height
    
    print(f"- Crop region: ({left}, {top}) to ({right}, {bottom})")
    print(f"- Lips will be at center (48, 32) in output")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Crop the region
        cropped = frame[top:bottom, left:right]
        
        if cropped.size > 0:
            # Resize to final dimensions
            resized = cv2.resize(cropped, (96, 64))
            out.write(resized)
        else:
            # Fallback black frame
            black_frame = np.zeros((64, 96, 3), dtype=np.uint8)
            out.write(black_frame)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"✅ Successfully created: {output_path}")
    return True

def main():
    if len(sys.argv) != 3:
        print("FINAL LIP VIDEO CREATOR")
        print("=======================")
        print()
        print("Usage: python3 create_final_lip_video.py <x> <y>")
        print()
        print("Where <x> <y> is the lip position you identified from the test crops.")
        print()
        print("For example:")
        print("- If test_crop_200_120.jpg showed the lips best:")
        print("  python3 create_final_lip_video.py 200 120")
        print()
        print("- If test_crop_150_090.jpg showed the lips best:")
        print("  python3 create_final_lip_video.py 150 90")
        print()
        print("Available test crops to check:")
        import os
        test_crops = [f for f in os.listdir('.') if f.startswith('test_crop_') and f.endswith('.jpg')]
        test_crops.sort()
        for crop in test_crops:
            # Extract position from filename
            parts = crop.replace('test_crop_', '').replace('.jpg', '').split('_')
            if len(parts) == 2:
                print(f"  {crop} -> position ({parts[0]}, {parts[1]})")
        return
    
    try:
        lip_x = int(sys.argv[1])
        lip_y = int(sys.argv[2])
    except ValueError:
        print("Error: x and y must be integers")
        return
    
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    output_video = f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_final_lips_{lip_x}_{lip_y}.mp4"
    
    print(f"Creating final video with lips at ({lip_x}, {lip_y})...")
    
    success = create_speaking_lips_video(input_video, output_video, lip_x, lip_y, zoom_factor=0.7)
    
    if success:
        print(f"\n🎯 SUCCESS! Created final video:")
        print(f"   {output_video}")
        print(f"\nThis video has:")
        print(f"✅ Lips centered at (48, 32) in 96x64 frame")
        print(f"✅ Extra room (zoom factor 0.7) for speech movement")
        print(f"✅ Lips should stay in frame during speaking")
        
        # Also create a version with even more room if needed
        extra_room_output = f"/Users/<USER>/Desktop/preprocessing_pipeline/doctor_extra_room_lips_{lip_x}_{lip_y}.mp4"
        print(f"\nCreating version with extra room for safety...")
        create_speaking_lips_video(input_video, extra_room_output, lip_x, lip_y, zoom_factor=0.8)
        print(f"✅ Also created: {extra_room_output}")
        print(f"   (Even more room - zoom factor 0.8)")
    else:
        print("❌ Failed to create video")

if __name__ == "__main__":
    main()
