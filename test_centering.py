#!/usr/bin/env python3
"""
Test script to verify lip centering is working correctly.
Creates a single frame with visual markers to check positioning.
"""

import cv2
import numpy as np
import sys

def test_centering():
    # Load the original video
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    cap = cv2.VideoCapture(input_video)
    
    # Get a sample frame (frame 20)
    cap.set(cv2.CAP_PROP_POS_FRAMES, 20)
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        print("Could not read frame")
        return
    
    # Use the same lip position we detected
    lip_center_x, lip_center_y = 143, 189
    width, height = 96, 64
    
    print(f"Original frame size: {frame.shape[1]}x{frame.shape[0]}")
    print(f"Lip position in original: ({lip_center_x}, {lip_center_y})")
    
    # Apply the same cropping logic
    target_center_x = width // 2   # 48
    target_center_y = height // 2  # 32
    
    crop_left = lip_center_x - target_center_x    # 143 - 48 = 95
    crop_right = lip_center_x + target_center_x   # 143 + 48 = 191
    crop_top = lip_center_y - target_center_y     # 189 - 32 = 157
    crop_bottom = lip_center_y + target_center_y  # 189 + 32 = 221
    
    print(f"Crop region: ({crop_left}, {crop_top}) to ({crop_right}, {crop_bottom})")
    
    # Crop the frame
    cropped = frame[crop_top:crop_bottom, crop_left:crop_right]
    print(f"Cropped frame size: {cropped.shape[1]}x{cropped.shape[0]}")
    
    # Now add visual markers to verify positioning
    marked_frame = cropped.copy()
    
    # Draw crosshairs at the center (where lips should be)
    center_x, center_y = 48, 32
    
    # Draw horizontal line through center
    cv2.line(marked_frame, (0, center_y), (width-1, center_y), (0, 255, 0), 1)
    # Draw vertical line through center  
    cv2.line(marked_frame, (center_x, 0), (center_x, height-1), (0, 255, 0), 1)
    
    # Draw a small circle at the exact center
    cv2.circle(marked_frame, (center_x, center_y), 3, (0, 255, 0), -1)
    
    # Add text labels
    cv2.putText(marked_frame, "CENTER", (center_x-20, center_y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)
    cv2.putText(marked_frame, f"({center_x},{center_y})", (center_x-15, center_y+15), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 0), 1)
    
    # Calculate where the original lip position should appear in the cropped frame
    lip_in_crop_x = lip_center_x - crop_left  # Should be 48
    lip_in_crop_y = lip_center_y - crop_top   # Should be 32
    
    print(f"Lip position in cropped frame should be: ({lip_in_crop_x}, {lip_in_crop_y})")
    print(f"Target center position: ({center_x}, {center_y})")
    
    if lip_in_crop_x == center_x and lip_in_crop_y == center_y:
        print("✅ MATH IS CORRECT - Lips should be at center!")
    else:
        print("❌ MATH ERROR - Lips are not at center")
    
    # Save both versions
    cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/test_cropped_plain.jpg", cropped)
    cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/test_cropped_marked.jpg", marked_frame)
    
    print("Saved test images:")
    print("- test_cropped_plain.jpg (plain cropped frame)")
    print("- test_cropped_marked.jpg (with center markers)")

if __name__ == "__main__":
    test_centering()
