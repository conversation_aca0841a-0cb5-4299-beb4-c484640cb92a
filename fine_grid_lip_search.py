#!/usr/bin/env python3
"""
Create a much finer grid of test positions to find the lips.
This will create many more test crops with smaller spacing.
"""

import cv2
import numpy as np

def create_fine_grid_test_crops():
    """
    Create test crops with much finer spacing to find lips.
    """
    video_path = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("Could not open video")
        return
    
    # Get a frame from the middle of the video
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    middle_frame = total_frames // 2
    
    cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
    ret, frame = cap.read()
    
    if not ret:
        print("Could not read frame")
        return
    
    frame_h, frame_w = frame.shape[:2]
    print(f"Frame size: {frame_w}x{frame_h}")
    
    # Create a much finer grid - every 20 pixels
    test_positions = []
    
    # Cover the entire frame with 20-pixel spacing
    for x in range(40, frame_w-40, 20):  # x from 40 to 360 in steps of 20
        for y in range(40, frame_h-40, 20):  # y from 40 to 160 in steps of 20
            test_positions.append((x, y))
    
    print(f"Creating {len(test_positions)} fine-grid test crops...")
    
    # Clear old test crops first
    import os
    old_crops = [f for f in os.listdir('.') if f.startswith('fine_crop_') and f.endswith('.jpg')]
    for old_crop in old_crops:
        os.remove(old_crop)
    
    # Create crops for each position
    crop_size = 63  # This is what we calculated for 0.7 zoom factor
    crop_height = 45  # 64 * 0.7
    
    created_count = 0
    for i, (x, y) in enumerate(test_positions):
        # Calculate crop boundaries
        left = max(0, x - crop_size // 2)
        right = min(frame_w, left + crop_size)
        top = max(0, y - crop_height // 2)
        bottom = min(frame_h, top + crop_height)
        
        # Ensure we have enough crop area
        if right - left < 30 or bottom - top < 20:
            continue
        
        # Crop and resize
        cropped = frame[top:bottom, left:right]
        if cropped.size > 0:
            resized = cv2.resize(cropped, (96, 64))
            
            # Add position text
            cv2.putText(resized, f"({x},{y})", (2, 12), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 255), 1)
            
            # Save crop
            filename = f"/Users/<USER>/Desktop/preprocessing_pipeline/fine_crop_{x:03d}_{y:03d}.jpg"
            cv2.imwrite(filename, resized)
            created_count += 1
    
    cap.release()
    print(f"Created {created_count} fine-grid test crops")
    print("Files named: fine_crop_XXX_YYY.jpg where XXX,YYY is the center position")
    
    # Also create a grid visualization
    create_grid_visualization(frame, test_positions)

def create_grid_visualization(frame, positions):
    """
    Create a visualization showing all test positions on the full frame.
    """
    vis_frame = frame.copy()
    
    # Draw all test positions
    for x, y in positions:
        cv2.circle(vis_frame, (x, y), 3, (0, 255, 0), 1)
        cv2.putText(vis_frame, f"{x},{y}", (x-15, y-5), cv2.FONT_HERSHEY_SIMPLEX, 0.2, (255, 255, 255), 1)
    
    # Draw a grid overlay
    frame_h, frame_w = frame.shape[:2]
    for x in range(0, frame_w, 50):
        cv2.line(vis_frame, (x, 0), (x, frame_h), (100, 100, 100), 1)
    for y in range(0, frame_h, 50):
        cv2.line(vis_frame, (0, y), (frame_w, y), (100, 100, 100), 1)
    
    cv2.imwrite("/Users/<USER>/Desktop/preprocessing_pipeline/grid_visualization.jpg", vis_frame)
    print("Created grid_visualization.jpg showing all test positions")

def create_region_samples():
    """
    Create larger samples from different regions of the frame.
    """
    video_path = "/Users/<USER>/Desktop/preprocessing_pipeline/doctor__useruser01__18to39__male__not_specified__20250824T025330.mp4"
    
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    middle_frame = total_frames // 2
    
    cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
    ret, frame = cap.read()
    cap.release()
    
    if not ret:
        return
    
    frame_h, frame_w = frame.shape[:2]
    
    # Create larger samples from different regions
    regions = [
        ("top_left", 0, 0, frame_w//2, frame_h//2),
        ("top_right", frame_w//2, 0, frame_w, frame_h//2),
        ("bottom_left", 0, frame_h//2, frame_w//2, frame_h),
        ("bottom_right", frame_w//2, frame_h//2, frame_w, frame_h),
        ("center", frame_w//4, frame_h//4, 3*frame_w//4, 3*frame_h//4),
        ("left_center", 0, frame_h//4, frame_w//2, 3*frame_h//4),
        ("right_center", frame_w//2, frame_h//4, frame_w, 3*frame_h//4),
    ]
    
    print("Creating region samples...")
    for name, x1, y1, x2, y2 in regions:
        region = frame[y1:y2, x1:x2]
        if region.size > 0:
            # Resize to a reasonable viewing size
            height, width = region.shape[:2]
            if width > 200:
                scale = 200 / width
                new_width = 200
                new_height = int(height * scale)
                region = cv2.resize(region, (new_width, new_height))
            
            filename = f"/Users/<USER>/Desktop/preprocessing_pipeline/region_{name}.jpg"
            cv2.imwrite(filename, region)
    
    print("Created region samples: region_*.jpg")

def main():
    print("FINE GRID LIP SEARCH")
    print("====================")
    print("Creating a much finer grid of test positions...")
    
    # Create fine grid test crops
    create_fine_grid_test_crops()
    
    # Create region samples for easier browsing
    create_region_samples()
    
    print("\n📁 Files created:")
    print("- fine_crop_XXX_YYY.jpg - Fine grid test crops (many files)")
    print("- grid_visualization.jpg - Shows all test positions on full frame")
    print("- region_*.jpg - Larger samples from different areas")
    
    print("\n🔍 How to find lips:")
    print("1. Look at region_*.jpg files to identify which area has the person")
    print("2. Look at grid_visualization.jpg to see the test positions")
    print("3. Browse fine_crop_*.jpg files in the area where you saw the person")
    print("4. Find the crop that shows the lips and note the position")

if __name__ == "__main__":
    main()
