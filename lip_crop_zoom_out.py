#!/usr/bin/env python3
"""
Zoom out version of the successful bbaf2n_lips_manual_down15_zoom60.mp4
Same detection and centering, but 10% more zoomed out for extra room around lips.
"""

import cv2
import numpy as np
import os
import sys

def find_face_and_estimate_lips(frame):
    """
    Use face detection to find the face, then estimate lip position in lower part of face.
    """
    # Load face cascade
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    faces = face_cascade.detectMultiScale(gray, 1.3, 5)
    
    if len(faces) == 0:
        return None, None
    
    # Use the largest face
    face = max(faces, key=lambda x: x[2] * x[3])
    x, y, w, h = face
    
    # Estimate lip position as lower center of face
    # Lips are typically at about 75-85% down from top of face
    lip_x = x + w // 2
    lip_y = y + int(h * 0.8)  # 80% down from top of face
    
    return lip_x, lip_y

def find_optimal_lip_position_manual(video_path):
    """
    Find lip position using face detection and manual estimation.
    """
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Sample frames throughout video
    sample_frames = np.linspace(10, total_frames-10, 10, dtype=int)
    
    detected_positions = []
    
    print("Finding faces and estimating lip positions...")
    
    for frame_num in sample_frames:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            lip_x, lip_y = find_face_and_estimate_lips(frame)
            if lip_x is not None and lip_y is not None:
                detected_positions.append((lip_x, lip_y))
                print(f"Frame {frame_num}: Estimated lips at ({lip_x}, {lip_y})")
    
    cap.release()
    
    if not detected_positions:
        print("Could not detect faces in any frames!")
        return None, None
    
    # Use median position
    x_positions = [pos[0] for pos in detected_positions]
    y_positions = [pos[1] for pos in detected_positions]
    
    median_x = int(np.median(x_positions))
    median_y = int(np.median(y_positions))
    
    print(f"Estimated lip center: ({median_x}, {median_y})")
    return median_x, median_y

def crop_lips_centered_zoomed_out(frame, lip_center_x, lip_center_y, width=96, height=64, zoom_factor=0.66):
    """
    Crop with lips centered and zoomed out 10% from the successful 0.6 zoom factor.
    0.6 -> 0.66 = 10% zoom out (larger crop area = more room around lips)
    """
    frame_h, frame_w = frame.shape[:2]
    
    # Calculate crop size for zoom effect
    crop_width = int(width * zoom_factor)
    crop_height = int(height * zoom_factor)
    
    print(f"DEBUG: Zoom factor: {zoom_factor} (10% zoomed out from 0.6)")
    print(f"DEBUG: Crop size: {crop_width}x{crop_height} (will be scaled to {width}x{height})")
    print(f"DEBUG: Lip position: ({lip_center_x}, {lip_center_y})")
    
    # Calculate crop boundaries with lips at center
    half_crop_w = crop_width // 2
    half_crop_h = crop_height // 2
    
    crop_left = lip_center_x - half_crop_w
    crop_right = lip_center_x + half_crop_w
    crop_top = lip_center_y - half_crop_h
    crop_bottom = lip_center_y + half_crop_h
    
    print(f"DEBUG: Calculated crop region: ({crop_left}, {crop_top}) to ({crop_right}, {crop_bottom})")
    
    # Ensure we don't go outside the original frame boundaries
    actual_left = max(0, crop_left)
    actual_right = min(frame_w, crop_right)
    actual_top = max(0, crop_top)
    actual_bottom = min(frame_h, crop_bottom)
    
    print(f"DEBUG: Adjusted crop region: ({actual_left}, {actual_top}) to ({actual_right}, {actual_bottom})")
    
    # Crop the region
    cropped = frame[actual_top:actual_bottom, actual_left:actual_right]
    actual_height, actual_width = cropped.shape[:2]
    
    print(f"DEBUG: Cropped region size: {actual_width}x{actual_height}")
    
    if cropped.size == 0:
        # Fallback to black frame
        return np.zeros((height, width, 3), dtype=np.uint8)
    
    # Scale to target size
    zoomed = cv2.resize(cropped, (width, height))
    
    print(f"DEBUG: Final output size: {zoomed.shape[1]}x{zoomed.shape[0]}")
    
    return zoomed

def process_video_zoomed_out(input_path, output_path, y_offset=15, zoom_factor=0.66):
    """
    Process video with the successful approach but zoomed out 10%.
    """
    # Find base lip position using face detection
    base_lip_x, base_lip_y = find_optimal_lip_position_manual(input_path)
    
    if base_lip_x is None:
        print("Could not detect faces!")
        return False
    
    # Apply manual offset to target actual lips (same as successful version)
    adjusted_lip_x = base_lip_x
    adjusted_lip_y = base_lip_y + y_offset
    
    print(f"Base lip estimate: ({base_lip_x}, {base_lip_y})")
    print(f"Y offset: {y_offset}")
    print(f"Final lip position: ({adjusted_lip_x}, {adjusted_lip_y})")
    print(f"Zoom factor: {zoom_factor} (10% zoomed out from successful 0.6)")
    
    # Process video
    cap = cv2.VideoCapture(input_path)
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (96, 64))
    
    print(f"\nProcessing {total_frames} frames...")
    print("Lips will be centered with 10% more room around them")
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Show debug info only for first frame
        if frame_count == 1:
            print(f"\nDEBUG INFO FOR FIRST FRAME:")
            cropped = crop_lips_centered_zoomed_out(frame, adjusted_lip_x, adjusted_lip_y, 96, 64, zoom_factor)
        else:
            # No debug for other frames - use simplified version
            crop_width = int(96 * zoom_factor)
            crop_height = int(64 * zoom_factor)
            
            crop_left = max(0, adjusted_lip_x - crop_width // 2)
            crop_right = min(frame.shape[1], crop_left + crop_width)
            crop_top = max(0, adjusted_lip_y - crop_height // 2)
            crop_bottom = min(frame.shape[0], crop_top + crop_height)
            
            cropped_region = frame[crop_top:crop_bottom, crop_left:crop_right]
            if cropped_region.size > 0:
                cropped = cv2.resize(cropped_region, (96, 64))
            else:
                cropped = np.zeros((64, 96, 3), dtype=np.uint8)
        
        out.write(cropped)
        
        if frame_count % 25 == 0:
            progress = (frame_count / total_frames) * 100
            print(f"Progress: {progress:.1f}%")
    
    cap.release()
    out.release()
    
    print(f"Complete! Saved: {output_path}")
    return True

def main():
    input_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n.mpg"
    output_video = "/Users/<USER>/Desktop/preprocessing_pipeline/bbaf2n_lips_zoomed_out_10percent.mp4"
    
    print("Creating zoomed out version of the successful approach...")
    print("Same detection and centering as bbaf2n_lips_manual_down15_zoom60.mp4")
    print("But with 10% zoom out (0.6 -> 0.66) for more room around lips")
    
    success = process_video_zoomed_out(input_video, output_video, y_offset=15, zoom_factor=0.66)
    
    if success:
        print(f"\n✅ Success! Created: bbaf2n_lips_zoomed_out_10percent.mp4")
        print("This should have lips centered with 10% more room around them!")
    else:
        print(f"\n❌ Failed to create video")

if __name__ == "__main__":
    main()
